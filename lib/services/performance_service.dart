import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:device_info_plus/device_info_plus.dart';

class PerformanceService {
  static final PerformanceService _instance = PerformanceService._internal();
  factory PerformanceService() => _instance;
  PerformanceService._internal();

  final String _baseUrl = 'https://api.buildbid.com/v1/performance';
  final DeviceInfoPlugin _deviceInfo = DeviceInfoPlugin();
  
  // Performance monitoring for 10M users
  Future<void> reportPerformanceMetric(String metricName, double value, Map<String, dynamic> context) async {
    try {
      final deviceData = await _getDeviceInfo();
      
      final response = await http.post(
        Uri.parse('$_baseUrl/metrics'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${await _getAuthToken()}',
        },
        body: json.encode({
          'metric_name': metricName,
          'value': value,
          'context': context,
          'device_info': deviceData,
          'timestamp': DateTime.now().toIso8601String(),
          'user_id': await _getUserId(),
          'app_version': '1.0.0',
        }),
      );

      if (response.statusCode != 200) {
        print('Performance metric failed: ${response.body}');
      }
    } catch (e) {
      print('Performance reporting error: $e');
    }
  }

  // Monitor app startup performance
  Future<void> reportStartupMetrics(Duration startupTime, Map<String, dynamic> details) async {
    await reportPerformanceMetric('app_startup_time', startupTime.inMilliseconds.toDouble(), {
      'startup_details': details,
      'startup_type': details['startup_type'] ?? 'unknown',
    });
  }

  // Monitor screen rendering performance
  Future<void> reportScreenMetrics(String screenName, Duration renderTime, int frameCount) async {
    await reportPerformanceMetric('screen_render_time', renderTime.inMilliseconds.toDouble(), {
      'screen_name': screenName,
      'frame_count': frameCount,
      'fps': frameCount / (renderTime.inSeconds > 0 ? renderTime.inSeconds : 1),
    });
  }

  // Monitor API response times
  Future<void> reportApiMetrics(String endpoint, Duration responseTime, int statusCode, int payloadSize) async {
    await reportPerformanceMetric('api_response_time', responseTime.inMilliseconds.toDouble(), {
      'endpoint': endpoint,
      'status_code': statusCode,
      'payload_size': payloadSize,
      'success': statusCode >= 200 && statusCode < 300,
    });
  }

  // Monitor database operation performance
  Future<void> reportDatabaseMetrics(String operation, String collection, Duration executionTime, bool success) async {
    await reportPerformanceMetric('database_operation_time', executionTime.inMilliseconds.toDouble(), {
      'operation': operation,
      'collection': collection,
      'success': success,
      'database_type': 'firestore',
    });
  }

  // Monitor memory usage
  Future<void> reportMemoryMetrics(String context, int memoryUsageMB) async {
    await reportPerformanceMetric('memory_usage', memoryUsageMB.toDouble(), {
      'context': context,
      'memory_unit': 'MB',
    });
  }

  // Monitor network performance
  Future<void> reportNetworkMetrics(String networkType, double bandwidth, int latency) async {
    await reportPerformanceMetric('network_performance', bandwidth, {
      'network_type': networkType,
      'latency_ms': latency,
      'bandwidth_unit': 'mbps',
    });
  }

  // Monitor battery usage
  Future<void> reportBatteryMetrics(String context, double batteryLevel, bool isCharging) async {
    await reportPerformanceMetric('battery_usage', batteryLevel, {
      'context': context,
      'is_charging': isCharging,
      'battery_unit': 'percentage',
    });
  }

  // Monitor crash metrics
  Future<void> reportCrashMetrics(String crashType, String errorMessage, Map<String, dynamic> stackTrace) async {
    try {
      await http.post(
        Uri.parse('$_baseUrl/crashes'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${await _getAuthToken()}',
        },
        body: json.encode({
          'crash_type': crashType,
          'error_message': errorMessage,
          'stack_trace': stackTrace,
          'device_info': await _getDeviceInfo(),
          'timestamp': DateTime.now().toIso8601String(),
          'user_id': await _getUserId(),
          'app_version': '1.0.0',
        }),
      );
    } catch (e) {
      print('Crash reporting error: $e');
    }
  }

  // Monitor user experience metrics
  Future<void> reportUXMetrics(String interaction, Duration responseTime, bool successful) async {
    await reportPerformanceMetric('user_interaction_time', responseTime.inMilliseconds.toDouble(), {
      'interaction_type': interaction,
      'successful': successful,
      'ux_category': 'user_interaction',
    });
  }

  // Get device information for context
  Future<Map<String, dynamic>> _getDeviceInfo() async {
    try {
      if (Platform.isAndroid) {
        final androidInfo = await _deviceInfo.androidInfo;
        return {
          'platform': 'android',
          'model': androidInfo.model,
          'manufacturer': androidInfo.manufacturer,
          'version': androidInfo.version.release,
          'sdk_int': androidInfo.version.sdkInt,
          'brand': androidInfo.brand,
          'device': androidInfo.device,
        };
      } else if (Platform.isIOS) {
        final iosInfo = await _deviceInfo.iosInfo;
        return {
          'platform': 'ios',
          'model': iosInfo.model,
          'name': iosInfo.name,
          'system_name': iosInfo.systemName,
          'system_version': iosInfo.systemVersion,
          'localized_model': iosInfo.localizedModel,
        };
      }
    } catch (e) {
      print('Device info error: $e');
    }
    
    return {
      'platform': Platform.operatingSystem,
      'version': Platform.operatingSystemVersion,
    };
  }

  // Helper methods
  Future<String> _getAuthToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('auth_token') ?? '';
  }

  Future<String> _getUserId() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('user_id') ?? 'anonymous';
  }

  // Performance monitoring dashboard data
  Future<Map<String, dynamic>> getPerformanceDashboard() async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/dashboard'),
        headers: {
          'Authorization': 'Bearer ${await _getAuthToken()}',
        },
      );

      if (response.statusCode == 200) {
        return json.decode(response.body);
      }
    } catch (e) {
      print('Performance dashboard error: $e');
    }
    
    return {};
  }

  // Real-time performance alerts
  Future<void> checkPerformanceAlerts() async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/alerts'),
        headers: {
          'Authorization': 'Bearer ${await _getAuthToken()}',
        },
      );

      if (response.statusCode == 200) {
        final alerts = json.decode(response.body);
        // Handle performance alerts
        _handlePerformanceAlerts(alerts);
      }
    } catch (e) {
      print('Performance alerts error: $e');
    }
  }

  void _handlePerformanceAlerts(Map<String, dynamic> alerts) {
    // Process performance alerts
    if (alerts['critical_alerts'] != null) {
      print('Critical performance alerts: ${alerts['critical_alerts']}');
    }
  }
}
