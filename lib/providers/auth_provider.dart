import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/user_model.dart';
import '../services/auth_service_simple.dart';

class AuthProvider extends ChangeNotifier {
  final AuthService _authService = AuthService();

  UserModel? _currentUser;
  bool _isLoading = false;
  String? _errorMessage;
  String? _verificationId;
  bool _isInitialized = false;
  String? _pendingPhoneNumber;
  String? _pendingUserRole;

  // Getters
  UserModel? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get isAuthenticated => _currentUser != null;
  bool get isInitialized => _isInitialized;
  String? get verificationId => _verificationId;
  String? get pendingPhoneNumber => _pendingPhoneNumber;

  // Initialize auth state
  Future<void> initializeAuth() async {
    try {
      await _loadUserFromStorage();
      _isInitialized = true;
      notifyListeners();

      _authService.authStateChanges.listen((UserModel? user) async {
        _currentUser = user;
        if (user != null) {
          await _saveUserToStorage(user);
        } else {
          await _clearUserFromStorage();
        }
        notifyListeners();
      });
    } catch (e) {
      _isInitialized = true;
      notifyListeners();
    }
  }

  // Save user to local storage
  Future<void> _saveUserToStorage(UserModel user) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = json.encode(user.toMap());
      await prefs.setString('current_user', userJson);
      await prefs.setBool('is_authenticated', true);
    } catch (e) {
      debugPrint('Error saving user to storage: $e');
    }
  }

  // Load user from local storage
  Future<void> _loadUserFromStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final isAuthenticated = prefs.getBool('is_authenticated') ?? false;

      if (isAuthenticated) {
        final userJson = prefs.getString('current_user');
        if (userJson != null) {
          final userMap = json.decode(userJson) as Map<String, dynamic>;
          _currentUser = UserModel.fromMap(userMap, userMap['id'] ?? userMap['uid'] ?? '');
        }
      }
    } catch (e) {
      debugPrint('Error loading user from storage: $e');
    }
  }

  // Clear user from local storage
  Future<void> _clearUserFromStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('current_user');
      await prefs.setBool('is_authenticated', false);
    } catch (e) {
      debugPrint('Error clearing user from storage: $e');
    }
  }

  // Load user profile
  Future<void> _loadUserProfile(String userId) async {
    try {
      _currentUser = await _authService.getUserProfile(userId);
      notifyListeners();
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
    }
  }

  // Send OTP
  Future<void> sendOTP(String phoneNumber, {String? userRole}) async {
    _setLoading(true);
    _clearError();

    _pendingPhoneNumber = phoneNumber;
    _pendingUserRole = userRole;

    try {
      await _authService.sendOTP(
        phoneNumber: phoneNumber,
        verificationCompleted: (String credential) async {
          // Auto-verification completed
          _setLoading(false);
        },
        verificationFailed: (String error) {
          _setError('Verification failed: $error');
          _setLoading(false);
        },
        codeSent: (String verificationId, int? resendToken) {
          _verificationId = verificationId;
          _setLoading(false);
        },
        codeAutoRetrievalTimeout: (String verificationId) {
          _verificationId = verificationId;
        },
      );
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
    }
  }

  // Verify OTP
  Future<bool> verifyOTP(String smsCode) async {
    if (_verificationId == null) {
      _setError('Verification ID not found. Please request OTP again.');
      return false;
    }

    _setLoading(true);
    _clearError();

    try {
      bool isVerified = await _authService.verifyOTP(
        verificationId: _verificationId!,
        smsCode: smsCode,
      );

      if (isVerified) {
        // Check if user profile exists
        bool profileExists = await _authService.userProfileExists('mock_user_id');

        if (profileExists) {
          _currentUser = await _authService.getUserProfile('mock_user_id');
        }

        _setLoading(false);
        return true;
      } else {
        _setError('Failed to verify OTP');
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
      return false;
    }
  }

  // Check if user profile exists
  Future<bool> checkUserProfileExists() async {
    return await _authService.userProfileExists('mock_user_id');
  }

  // Create user profile
  Future<bool> createUserProfile({
    required String name,
    required String userType,
    required String phoneNumber,
    String? email,
    String? address,
    String? city,
    String? state,
    String? pincode,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      UserModel userModel = await _authService.createUserProfile(
        name: name,
        userType: userType,
        phoneNumber: phoneNumber,
        email: email,
        address: address,
        city: city,
        state: state,
        pincode: pincode,
      );

      _currentUser = userModel;
      _setLoading(false);
      return true;
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
      return false;
    }
  }

  // Update user profile
  Future<bool> updateUserProfile(UserModel updatedUser) async {
    _setLoading(true);
    _clearError();

    try {
      await _authService.updateUserProfile(updatedUser);
      _currentUser = updatedUser;
      _setLoading(false);
      return true;
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
      return false;
    }
  }

  // Sign out
  Future<void> signOut() async {
    _setLoading(true);
    _clearError();

    try {
      await _authService.signOut();
      await _clearUserFromStorage();
      _currentUser = null;
      _verificationId = null;
      _pendingPhoneNumber = null;
      _pendingUserRole = null;
      _setLoading(false);
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
    }
  }

  // Delete account
  Future<bool> deleteAccount() async {
    _setLoading(true);
    _clearError();

    try {
      await _authService.deleteAccount();
      _currentUser = null;
      _verificationId = null;
      _setLoading(false);
      return true;
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
      return false;
    }
  }



  // Refresh user profile
  Future<void> refreshUserProfile() async {
    _currentUser = await _authService.getUserProfile('mock_user_id');
    notifyListeners();
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Clear verification ID
  void clearVerificationId() {
    _verificationId = null;
    notifyListeners();
  }

  // Get user type specific data
  bool get isBuilder => _currentUser?.userType == 'builder';
  bool get isMistri => _currentUser?.userType == 'mistri';
  bool get isVendor => _currentUser?.userType == 'vendor';

  // Get user display name
  String get displayName => _currentUser?.name ?? 'User';

  // Get user phone number
  String get phoneNumber => _currentUser?.phoneNumber ?? '';

  // Check if profile is complete
  bool get isProfileComplete {
    if (_currentUser == null) return false;

    return _currentUser!.name.isNotEmpty &&
           _currentUser!.phoneNumber.isNotEmpty &&
           _currentUser!.userType.isNotEmpty &&
           (_currentUser!.address?.isNotEmpty ?? false) &&
           (_currentUser!.city?.isNotEmpty ?? false) &&
           (_currentUser!.state?.isNotEmpty ?? false) &&
           (_currentUser!.pincode?.isNotEmpty ?? false);
  }
}
