const express = require('express');
const { body, query, validationResult } = require('express-validator');
const Order = require('../models/Order');
const Material = require('../models/Material');
const Vendor = require('../models/Vendor');
const { authenticateToken } = require('../middleware/auth');
const { sendOrderNotification } = require('../services/notificationService');
const router = express.Router();

/**
 * @swagger
 * /api/orders:
 *   post:
 *     summary: Create new order
 *     tags: [Orders]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - vendorId
 *               - items
 *               - delivery
 *               - payment
 *             properties:
 *               vendorId:
 *                 type: string
 *               items:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     materialId:
 *                       type: string
 *                     quantity:
 *                       type: number
 *               delivery:
 *                 type: object
 *                 properties:
 *                   address:
 *                     type: object
 *                   timeSlot:
 *                     type: object
 *               payment:
 *                 type: object
 *                 properties:
 *                   method:
 *                     type: string
 *                     enum: [cod, online, wallet, bank_transfer]
 *     responses:
 *       201:
 *         description: Order created successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 */
router.post('/', authenticateToken, [
  body('vendorId').isMongoId().withMessage('Valid vendor ID is required'),
  body('items').isArray({ min: 1 }).withMessage('At least one item is required'),
  body('items.*.materialId').isMongoId().withMessage('Valid material ID is required'),
  body('items.*.quantity').isInt({ min: 1 }).withMessage('Quantity must be at least 1'),
  body('delivery.address').isObject().withMessage('Delivery address is required'),
  body('payment.method').isIn(['cod', 'online', 'wallet', 'bank_transfer']).withMessage('Invalid payment method')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { vendorId, items, delivery, payment } = req.body;

    // Verify vendor exists
    const vendor = await Vendor.findById(vendorId);
    if (!vendor) {
      return res.status(400).json({
        success: false,
        message: 'Vendor not found'
      });
    }

    // Process items and calculate pricing
    const processedItems = [];
    let subtotal = 0;

    for (const item of items) {
      const material = await Material.findById(item.materialId);
      if (!material) {
        return res.status(400).json({
          success: false,
          message: `Material ${item.materialId} not found`
        });
      }

      if (!material.isInStock(item.quantity)) {
        return res.status(400).json({
          success: false,
          message: `Insufficient stock for ${material.name}`
        });
      }

      const pricePerUnit = material.getBulkPrice(item.quantity);
      const totalPrice = pricePerUnit * item.quantity;

      processedItems.push({
        materialId: material._id,
        name: material.name,
        quantity: item.quantity,
        unit: material.pricing.unit,
        pricePerUnit,
        totalPrice,
        specifications: material.specifications,
        images: material.images.map(img => img.url)
      });

      subtotal += totalPrice;

      // Reserve stock
      await material.reserveStock(item.quantity);
    }

    // Calculate taxes and charges
    const gstAmount = (subtotal * 18) / 100; // 18% GST
    const deliveryCharges = vendor.deliveryCharges || 0;
    const total = subtotal + gstAmount + deliveryCharges;

    // Create order
    const order = new Order({
      customerId: req.user.userId,
      vendorId,
      items: processedItems,
      pricing: {
        subtotal,
        tax: {
          gst: gstAmount,
          total: gstAmount
        },
        delivery: {
          charges: deliveryCharges,
          freeDelivery: subtotal >= (vendor.freeDeliveryThreshold || 0)
        },
        total
      },
      delivery,
      payment: {
        method: payment.method,
        status: payment.method === 'cod' ? 'pending' : 'pending'
      },
      status: 'pending'
    });

    await order.save();

    // Generate delivery OTP
    const otp = order.generateDeliveryOTP();
    await order.save();

    // Send notifications
    await sendOrderNotification(vendor.userId, 'new_order', {
      orderId: order._id,
      orderNumber: order.orderNumber,
      customerName: req.user.name,
      total: order.pricing.total
    });

    res.status(201).json({
      success: true,
      message: 'Order created successfully',
      data: {
        order,
        deliveryOTP: otp
      }
    });
  } catch (error) {
    console.error('Create order error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create order'
    });
  }
});

/**
 * @swagger
 * /api/orders:
 *   get:
 *     summary: Get user's orders
 *     tags: [Orders]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *         description: Filter by order status
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number (default: 1)
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Items per page (default: 20)
 *     responses:
 *       200:
 *         description: Orders retrieved successfully
 */
router.get('/', authenticateToken, [
  query('status').optional().isString(),
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 })
], async (req, res) => {
  try {
    const { status, page = 1, limit = 20 } = req.query;

    const query = { customerId: req.user.userId };
    if (status) {
      query.status = status;
    }

    const skip = (parseInt(page) - 1) * parseInt(limit);

    const orders = await Order.find(query)
      .populate('vendorId', 'businessName')
      .populate('items.materialId', 'name images')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    const totalCount = await Order.countDocuments(query);

    res.json({
      success: true,
      data: {
        orders,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(totalCount / parseInt(limit)),
          totalItems: totalCount,
          itemsPerPage: parseInt(limit)
        }
      }
    });
  } catch (error) {
    console.error('Get orders error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get orders'
    });
  }
});

/**
 * @swagger
 * /api/orders/{id}:
 *   get:
 *     summary: Get order by ID
 *     tags: [Orders]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Order ID
 *     responses:
 *       200:
 *         description: Order retrieved successfully
 *       404:
 *         description: Order not found
 */
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const order = await Order.findById(req.params.id)
      .populate('customerId', 'name phone')
      .populate('vendorId', 'businessName phone location')
      .populate('items.materialId', 'name images specifications');

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    // Check if user has access to this order
    if (order.customerId._id.toString() !== req.user.userId && 
        order.vendorId.userId.toString() !== req.user.userId) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    res.json({
      success: true,
      data: order
    });
  } catch (error) {
    console.error('Get order error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get order'
    });
  }
});

/**
 * @swagger
 * /api/orders/{id}/status:
 *   put:
 *     summary: Update order status
 *     tags: [Orders]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Order ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - status
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [confirmed, preparing, ready_for_pickup, out_for_delivery, delivered, cancelled]
 *               note:
 *                 type: string
 *     responses:
 *       200:
 *         description: Order status updated successfully
 *       400:
 *         description: Invalid status transition
 *       403:
 *         description: Access denied
 *       404:
 *         description: Order not found
 */
router.put('/:id/status', authenticateToken, [
  body('status').isIn([
    'confirmed', 'preparing', 'ready_for_pickup', 'out_for_delivery', 
    'delivered', 'cancelled'
  ]).withMessage('Invalid status'),
  body('note').optional().isString()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { status, note } = req.body;

    const order = await Order.findById(req.params.id).populate('vendorId');
    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    // Check if user is the vendor for this order
    if (order.vendorId.userId.toString() !== req.user.userId) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    // Update order status
    await order.updateStatus(status, note, req.user.userId);

    // Send notification to customer
    await sendOrderNotification(order.customerId, 'order_status_update', {
      orderId: order._id,
      orderNumber: order.orderNumber,
      status,
      note
    });

    res.json({
      success: true,
      message: 'Order status updated successfully',
      data: order
    });
  } catch (error) {
    console.error('Update order status error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update order status'
    });
  }
});

/**
 * @swagger
 * /api/orders/{id}/verify-delivery:
 *   post:
 *     summary: Verify delivery with OTP
 *     tags: [Orders]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Order ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - otp
 *             properties:
 *               otp:
 *                 type: string
 *     responses:
 *       200:
 *         description: Delivery verified successfully
 *       400:
 *         description: Invalid OTP
 *       404:
 *         description: Order not found
 */
router.post('/:id/verify-delivery', authenticateToken, [
  body('otp').isLength({ min: 4, max: 4 }).withMessage('OTP must be 4 digits')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { otp } = req.body;

    const order = await Order.findById(req.params.id);
    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    // Verify OTP
    if (!order.verifyDeliveryOTP(otp)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid OTP'
      });
    }

    await order.save();

    // Update material stock
    for (const item of order.items) {
      await Material.findByIdAndUpdate(item.materialId, {
        $inc: { 
          'inventory.currentStock': -item.quantity,
          'metrics.orders': 1
        },
        'metrics.lastOrdered': new Date()
      });
    }

    // Update vendor metrics
    await Vendor.findByIdAndUpdate(order.vendorId, {
      $inc: { 
        totalOrders: 1,
        completedOrders: 1
      }
    });

    res.json({
      success: true,
      message: 'Delivery verified successfully',
      data: order
    });
  } catch (error) {
    console.error('Verify delivery error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to verify delivery'
    });
  }
});

/**
 * @swagger
 * /api/orders/vendor/orders:
 *   get:
 *     summary: Get vendor's orders
 *     tags: [Orders]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *         description: Filter by order status
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number (default: 1)
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Items per page (default: 20)
 *     responses:
 *       200:
 *         description: Vendor orders retrieved successfully
 */
router.get('/vendor/orders', authenticateToken, async (req, res) => {
  try {
    // Get vendor profile
    const vendor = await Vendor.findOne({ userId: req.user.userId });
    if (!vendor) {
      return res.status(400).json({
        success: false,
        message: 'Vendor profile not found'
      });
    }

    const { status, page = 1, limit = 20 } = req.query;

    const query = { vendorId: vendor._id };
    if (status) {
      query.status = status;
    }

    const skip = (parseInt(page) - 1) * parseInt(limit);

    const orders = await Order.find(query)
      .populate('customerId', 'name phone location')
      .populate('items.materialId', 'name images')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    const totalCount = await Order.countDocuments(query);

    res.json({
      success: true,
      data: {
        orders,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(totalCount / parseInt(limit)),
          totalItems: totalCount,
          itemsPerPage: parseInt(limit)
        }
      }
    });
  } catch (error) {
    console.error('Get vendor orders error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get vendor orders'
    });
  }
});

module.exports = router;
