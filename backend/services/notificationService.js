const admin = require('firebase-admin');
const { sendOTP, sendOrderConfirmation, sendDeliveryNotification } = require('./smsService');

// Initialize Firebase Admin SDK only if credentials are provided
let firebaseInitialized = false;
if (!admin.apps.length &&
    process.env.FIREBASE_PROJECT_ID &&
    process.env.FIREBASE_PRIVATE_KEY &&
    process.env.FIREBASE_CLIENT_EMAIL) {
  try {
    admin.initializeApp({
      credential: admin.credential.cert({
        projectId: process.env.FIREBASE_PROJECT_ID,
        privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
        clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
      }),
    });
    firebaseInitialized = true;
  } catch (error) {
    console.log('Firebase not initialized - running in demo mode');
  }
}

/**
 * Send push notification to a single device
 * @param {string} fcmToken - FCM token
 * @param {object} notification - Notification data
 * @returns {Promise<string>} - Message ID
 */
const sendPushNotification = async (fcmToken, notification) => {
  try {
    if (!firebaseInitialized) {
      console.log(`[DEMO MODE] Push notification to ${fcmToken}: ${notification.title}`);
      return { messageId: 'demo_message_id' };
    }

    const message = {
      token: fcmToken,
      notification: {
        title: notification.title,
        body: notification.body,
        imageUrl: notification.imageUrl
      },
      data: notification.data || {},
      android: {
        notification: {
          icon: 'ic_notification',
          color: '#FF6B35', // BuildBid orange color
          sound: 'default',
          channelId: 'buildbid_notifications'
        }
      },
      apns: {
        payload: {
          aps: {
            sound: 'default',
            badge: 1
          }
        }
      }
    };

    const response = await admin.messaging().send(message);
    console.log('Push notification sent successfully:', response);
    return response;
  } catch (error) {
    console.error('Failed to send push notification:', error);
    throw new Error('Failed to send push notification');
  }
};

/**
 * Send push notification to multiple devices
 * @param {Array<string>} fcmTokens - Array of FCM tokens
 * @param {object} notification - Notification data
 * @returns {Promise<object>} - Batch response
 */
const sendBulkPushNotification = async (fcmTokens, notification) => {
  try {
    const message = {
      notification: {
        title: notification.title,
        body: notification.body,
        imageUrl: notification.imageUrl
      },
      data: notification.data || {},
      tokens: fcmTokens,
      android: {
        notification: {
          icon: 'ic_notification',
          color: '#FF6B35',
          sound: 'default',
          channelId: 'buildbid_notifications'
        }
      },
      apns: {
        payload: {
          aps: {
            sound: 'default',
            badge: 1
          }
        }
      }
    };

    const response = await admin.messaging().sendMulticast(message);
    console.log(`Bulk push notification: ${response.successCount} successful, ${response.failureCount} failed`);
    return response;
  } catch (error) {
    console.error('Failed to send bulk push notification:', error);
    throw new Error('Failed to send bulk push notification');
  }
};

/**
 * Send order notification (push + SMS)
 * @param {string} userId - User ID
 * @param {string} type - Notification type
 * @param {object} data - Notification data
 */
const sendOrderNotification = async (userId, type, data) => {
  try {
    const User = require('../models/User');
    const user = await User.findById(userId);
    
    if (!user) {
      throw new Error('User not found');
    }

    let notification = {};
    
    switch (type) {
      case 'new_order':
        notification = {
          title: 'New Order Received!',
          body: `Order #${data.orderNumber} from ${data.customerName} - ₹${data.total}`,
          data: {
            type: 'new_order',
            orderId: data.orderId.toString(),
            orderNumber: data.orderNumber
          }
        };
        break;
        
      case 'order_confirmed':
        notification = {
          title: 'Order Confirmed!',
          body: `Your order #${data.orderNumber} has been confirmed and will be delivered soon.`,
          data: {
            type: 'order_confirmed',
            orderId: data.orderId.toString(),
            orderNumber: data.orderNumber
          }
        };
        
        // Send SMS confirmation
        if (user.notifications.sms) {
          await sendOrderConfirmation(user.phone, {
            orderNumber: data.orderNumber,
            total: data.total,
            address: data.address,
            orderId: data.orderId
          });
        }
        break;
        
      case 'order_status_update':
        notification = {
          title: 'Order Status Updated',
          body: `Order #${data.orderNumber} is now ${data.status.replace('_', ' ')}`,
          data: {
            type: 'order_status_update',
            orderId: data.orderId.toString(),
            orderNumber: data.orderNumber,
            status: data.status
          }
        };
        break;
        
      case 'out_for_delivery':
        notification = {
          title: 'Order Out for Delivery!',
          body: `Your order #${data.orderNumber} is on the way. OTP: ${data.otp}`,
          data: {
            type: 'out_for_delivery',
            orderId: data.orderId.toString(),
            orderNumber: data.orderNumber,
            otp: data.otp
          }
        };
        
        // Send SMS with delivery details
        if (user.notifications.sms) {
          await sendDeliveryNotification(user.phone, {
            orderNumber: data.orderNumber,
            deliveryPerson: data.deliveryPerson,
            deliveryPhone: data.deliveryPhone,
            otp: data.otp
          });
        }
        break;
        
      case 'order_delivered':
        notification = {
          title: 'Order Delivered!',
          body: `Order #${data.orderNumber} has been delivered successfully. Please rate your experience.`,
          data: {
            type: 'order_delivered',
            orderId: data.orderId.toString(),
            orderNumber: data.orderNumber
          }
        };
        break;
        
      default:
        throw new Error('Invalid notification type');
    }

    // Send push notification if user has FCM token and push notifications enabled
    if (user.fcmToken && user.notifications.push) {
      await sendPushNotification(user.fcmToken, notification);
    }

    // Store notification in database
    await storeNotification(userId, notification);
    
  } catch (error) {
    console.error('Failed to send order notification:', error);
    throw error;
  }
};

/**
 * Send service booking notification
 * @param {string} userId - User ID
 * @param {string} type - Notification type
 * @param {object} data - Notification data
 */
const sendServiceNotification = async (userId, type, data) => {
  try {
    const User = require('../models/User');
    const user = await User.findById(userId);
    
    if (!user) {
      throw new Error('User not found');
    }

    let notification = {};
    
    switch (type) {
      case 'service_booked':
        notification = {
          title: 'Service Booked!',
          body: `${data.serviceName} booked for ${data.date} at ${data.time}`,
          data: {
            type: 'service_booked',
            bookingId: data.bookingId.toString(),
            serviceId: data.serviceId.toString()
          }
        };
        break;
        
      case 'service_confirmed':
        notification = {
          title: 'Service Confirmed!',
          body: `${data.providerName} confirmed your ${data.serviceName} booking`,
          data: {
            type: 'service_confirmed',
            bookingId: data.bookingId.toString(),
            serviceId: data.serviceId.toString()
          }
        };
        break;
        
      case 'service_started':
        notification = {
          title: 'Service Started!',
          body: `${data.providerName} has started working on your ${data.serviceName}`,
          data: {
            type: 'service_started',
            bookingId: data.bookingId.toString(),
            serviceId: data.serviceId.toString()
          }
        };
        break;
        
      case 'service_completed':
        notification = {
          title: 'Service Completed!',
          body: `${data.serviceName} has been completed. Please rate the service.`,
          data: {
            type: 'service_completed',
            bookingId: data.bookingId.toString(),
            serviceId: data.serviceId.toString()
          }
        };
        break;
        
      default:
        throw new Error('Invalid service notification type');
    }

    // Send push notification
    if (user.fcmToken && user.notifications.push) {
      await sendPushNotification(user.fcmToken, notification);
    }

    // Store notification in database
    await storeNotification(userId, notification);
    
  } catch (error) {
    console.error('Failed to send service notification:', error);
    throw error;
  }
};

/**
 * Store notification in database
 * @param {string} userId - User ID
 * @param {object} notification - Notification data
 */
const storeNotification = async (userId, notification) => {
  try {
    const Notification = require('../models/Notification');
    
    const dbNotification = new Notification({
      userId,
      title: notification.title,
      body: notification.body,
      data: notification.data,
      type: notification.data?.type || 'general',
      isRead: false
    });

    await dbNotification.save();
    return dbNotification;
  } catch (error) {
    console.error('Failed to store notification:', error);
    throw error;
  }
};

/**
 * Send promotional notification to multiple users
 * @param {Array<string>} userIds - Array of user IDs
 * @param {object} promotion - Promotion data
 */
const sendPromotionalNotification = async (userIds, promotion) => {
  try {
    const User = require('../models/User');
    const users = await User.find({
      _id: { $in: userIds },
      'notifications.push': true,
      fcmToken: { $exists: true, $ne: null }
    });

    const fcmTokens = users.map(user => user.fcmToken);
    
    const notification = {
      title: promotion.title,
      body: promotion.description,
      imageUrl: promotion.imageUrl,
      data: {
        type: 'promotion',
        promotionId: promotion.id,
        code: promotion.code
      }
    };

    if (fcmTokens.length > 0) {
      await sendBulkPushNotification(fcmTokens, notification);
    }

    // Store notifications in database
    const promises = userIds.map(userId => storeNotification(userId, notification));
    await Promise.all(promises);
    
  } catch (error) {
    console.error('Failed to send promotional notification:', error);
    throw error;
  }
};

module.exports = {
  sendPushNotification,
  sendBulkPushNotification,
  sendOrderNotification,
  sendServiceNotification,
  sendPromotionalNotification,
  storeNotification
};
