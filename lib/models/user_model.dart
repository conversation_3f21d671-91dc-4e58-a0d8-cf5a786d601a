// import 'package:cloud_firestore/cloud_firestore.dart';

class UserModel {
  final String id;
  final String uid; // Firebase Auth UID
  final String name;
  final String email;
  final String phone;
  final String role; // builder, mistri, vendor
  final String status; // active, blocked, pending
  final String language; // en, hi
  final Map<String, dynamic>? location;
  final String? profileImage;
  final double rating;
  final int totalOrders;
  final int completedOrders;
  final DateTime createdAt;
  final DateTime? lastLoginAt;
  final String? fcmToken;
  final Map<String, dynamic>? preferences;
  final bool isVerified;
  final String? businessName; // For vendors
  final List<String>? specializations; // For mistris/vendors

  UserModel({
    required this.id,
    required this.uid,
    required this.name,
    required this.email,
    required this.phone,
    required this.role,
    required this.status,
    this.language = 'en',
    this.location,
    this.profileImage,
    this.rating = 0.0,
    this.totalOrders = 0,
    this.completedOrders = 0,
    required this.createdAt,
    this.lastLoginAt,
    this.fcmToken,
    this.preferences,
    this.isVerified = false,
    this.businessName,
    this.specializations,
  });

  // factory UserModel.fromFirestore(DocumentSnapshot doc) {
  //   final data = doc.data() as Map<String, dynamic>;
  //   return UserModel.fromMap(data, doc.id);
  // }

  factory UserModel.fromMap(Map<String, dynamic> data, String id) {
    return UserModel(
      id: id,
      uid: data['uid'] ?? id,
      name: data['name'] ?? '',
      email: data['email'] ?? '',
      phone: data['phone'] ?? '',
      role: data['role'] ?? 'builder',
      status: data['status'] ?? 'active',
      language: data['language'] ?? 'en',
      location: data['location'],
      profileImage: data['profile_image'],
      rating: (data['rating'] ?? 0.0).toDouble(),
      totalOrders: data['total_orders'] ?? 0,
      completedOrders: data['completed_orders'] ?? 0,
      createdAt: data['created_at'] is String
          ? DateTime.parse(data['created_at'])
          : DateTime.now(),
      lastLoginAt: data['last_login_at'] is String
          ? DateTime.parse(data['last_login_at'])
          : null,
      fcmToken: data['fcm_token'],
      preferences: data['preferences'],
      isVerified: data['is_verified'] ?? false,
      businessName: data['business_name'],
      specializations: data['specializations'] != null 
          ? List<String>.from(data['specializations']) 
          : null,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'uid': uid,
      'name': name,
      'email': email,
      'phone': phone,
      'role': role,
      'status': status,
      'language': language,
      'location': location,
      'profile_image': profileImage,
      'rating': rating,
      'total_orders': totalOrders,
      'completed_orders': completedOrders,
      'created_at': createdAt.toIso8601String(),
      'last_login_at': lastLoginAt?.toIso8601String(),
      'fcm_token': fcmToken,
      'preferences': preferences,
      'is_verified': isVerified,
      'business_name': businessName,
      'specializations': specializations,
    };
  }

  UserModel copyWith({
    String? uid,
    String? name,
    String? email,
    String? phone,
    String? role,
    String? status,
    String? language,
    Map<String, dynamic>? location,
    String? profileImage,
    double? rating,
    int? totalOrders,
    int? completedOrders,
    DateTime? createdAt,
    DateTime? lastLoginAt,
    String? fcmToken,
    Map<String, dynamic>? preferences,
    bool? isVerified,
    String? businessName,
    List<String>? specializations,
  }) {
    return UserModel(
      id: id,
      uid: uid ?? this.uid,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      role: role ?? this.role,
      status: status ?? this.status,
      language: language ?? this.language,
      location: location ?? this.location,
      profileImage: profileImage ?? this.profileImage,
      rating: rating ?? this.rating,
      totalOrders: totalOrders ?? this.totalOrders,
      completedOrders: completedOrders ?? this.completedOrders,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      fcmToken: fcmToken ?? this.fcmToken,
      preferences: preferences ?? this.preferences,
      isVerified: isVerified ?? this.isVerified,
      businessName: businessName ?? this.businessName,
      specializations: specializations ?? this.specializations,
    );
  }

  String get displayRole {
    switch (role) {
      case 'builder':
        return 'Builder';
      case 'mistri':
        return 'Raj Mistri';
      case 'vendor':
        return 'Vendor';
      default:
        return role.toUpperCase();
    }
  }

  String get statusDisplay {
    switch (status) {
      case 'active':
        return 'Active';
      case 'blocked':
        return 'Blocked';
      case 'pending':
        return 'Pending';
      default:
        return status.toUpperCase();
    }
  }

  String get locationDisplay {
    if (location == null) return 'Not specified';
    final city = location!['city'] ?? '';
    final state = location!['state'] ?? '';
    final pincode = location!['pincode'] ?? '';
    if (pincode.isNotEmpty) {
      return '$city, $state - $pincode';
    }
    return '$city, $state'.trim().replaceAll(RegExp(r'^,|,$'), '');
  }

  String get ratingDisplay {
    return '${rating.toStringAsFixed(1)} ⭐';
  }

  double get completionRate {
    if (totalOrders == 0) return 0.0;
    return (completedOrders / totalOrders) * 100;
  }

  String get completionRateDisplay {
    return '${completionRate.toStringAsFixed(1)}%';
  }

  bool get isBuilder => role == 'builder';
  bool get isMistri => role == 'mistri';
  bool get isVendor => role == 'vendor';
  bool get isActive => status == 'active';
  bool get isBlocked => status == 'blocked';

  // Alias for role (for backward compatibility)
  String get userType => role;

  // Alias for phone (for backward compatibility)
  String get phoneNumber => phone;

  // Location getters for backward compatibility
  String? get address => location?['address'];
  String? get city => location?['city'];
  String? get state => location?['state'];
  String? get pincode => location?['pincode'];

  String get displayName {
    if (businessName != null && businessName!.isNotEmpty) {
      return businessName!;
    }
    return name;
  }

  List<String> get specializationsList {
    return specializations ?? [];
  }

  String get specializationsDisplay {
    if (specializations == null || specializations!.isEmpty) {
      return 'No specializations';
    }
    return specializations!.join(', ');
  }
}
