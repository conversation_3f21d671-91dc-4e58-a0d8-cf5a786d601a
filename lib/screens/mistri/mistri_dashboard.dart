import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../providers/language_provider.dart';
import '../../utils/app_colors.dart';
import '../../widgets/bottom_nav_bar.dart';
import '../../widgets/custom_search_field.dart';
import '../common/login_screen.dart';
import 'home_screen.dart';
import 'product_list_screen.dart';
import 'cart_screen.dart';
import 'order_tracking_screen.dart';
import '../common/profile_screen.dart';

class MistriDashboard extends StatefulWidget {
  const MistriDashboard({Key? key}) : super(key: key);

  @override
  State<MistriDashboard> createState() => _MistriDashboardState();
}

class _MistriDashboardState extends State<MistriDashboard> {
  int _selectedIndex = 0;

  @override
  Widget build(BuildContext context) {
    final List<Widget> pages = [
      const HomeScreen(),
      const ProductListScreen(),
      const CartScreen(),
      const OrderTrackingScreen(orderId: 'sample_order'),
      const ProfileScreen(),
    ];

    return Scaffold(
      body: pages[_selectedIndex],
      bottomNavigationBar: MistriBottomNavBar(
        currentIndex: _selectedIndex,
        onTap: (index) {
          setState(() {
            _selectedIndex = index;
          });
        },
        cartCount: 3, // Mock cart count - replace with actual data
      ),
    );
  }
}
