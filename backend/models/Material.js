const mongoose = require('mongoose');

const materialSchema = new mongoose.Schema({
  // Basic Information
  name: {
    type: String,
    required: [true, 'Material name is required'],
    trim: true,
    maxlength: [200, 'Material name cannot exceed 200 characters']
  },
  description: {
    type: String,
    required: [true, 'Material description is required'],
    maxlength: [1000, 'Description cannot exceed 1000 characters']
  },
  
  // Vendor Reference
  vendorId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Vendor',
    required: true
  },
  
  // Category and Classification
  category: {
    type: String,
    required: [true, 'Category is required'],
    enum: [
      'bricks', 'cement', 'steel', 'sand', 'gravel', 'stone',
      'paints', 'tiles', 'marble', 'granite', 'hardware',
      'plumbing', 'electrical', 'roofing', 'flooring', 'other'
    ]
  },
  subcategory: {
    type: String,
    trim: true
  },
  brand: {
    type: String,
    trim: true
  },
  model: {
    type: String,
    trim: true
  },
  
  // Specifications
  specifications: {
    dimensions: {
      length: Number,
      width: Number,
      height: Number,
      unit: { type: String, enum: ['mm', 'cm', 'm', 'inch', 'ft'] }
    },
    weight: {
      value: Number,
      unit: { type: String, enum: ['g', 'kg', 'ton', 'lb'] }
    },
    material: String, // e.g., "Clay", "Concrete", "Steel"
    grade: String, // e.g., "Grade A", "53 Grade"
    color: String,
    finish: String,
    strength: String,
    other: [{
      key: String,
      value: String
    }]
  },
  
  // Pricing
  pricing: {
    basePrice: {
      type: Number,
      required: [true, 'Base price is required'],
      min: [0, 'Price cannot be negative']
    },
    unit: {
      type: String,
      required: [true, 'Unit is required'],
      enum: ['piece', 'bag', 'ton', 'cubic_meter', 'square_meter', 'liter', 'kg', 'meter', 'box', 'bundle']
    },
    minimumQuantity: {
      type: Number,
      default: 1,
      min: [1, 'Minimum quantity must be at least 1']
    },
    bulkPricing: [{
      minQuantity: Number,
      maxQuantity: Number,
      pricePerUnit: Number,
      discount: Number // percentage
    }],
    tax: {
      gst: { type: Number, default: 18 }, // GST percentage
      other: Number
    }
  },
  
  // Inventory
  inventory: {
    currentStock: {
      type: Number,
      required: [true, 'Current stock is required'],
      min: [0, 'Stock cannot be negative']
    },
    reservedStock: {
      type: Number,
      default: 0,
      min: [0, 'Reserved stock cannot be negative']
    },
    lowStockThreshold: {
      type: Number,
      default: 10,
      min: [0, 'Low stock threshold cannot be negative']
    },
    reorderLevel: {
      type: Number,
      default: 5,
      min: [0, 'Reorder level cannot be negative']
    },
    lastRestocked: Date,
    expiryDate: Date
  },
  
  // Images
  images: [{
    url: {
      type: String,
      required: true
    },
    publicId: String,
    caption: String,
    isPrimary: { type: Boolean, default: false },
    order: { type: Number, default: 0 }
  }],
  
  // Quality and Certifications
  quality: {
    grade: String,
    certifications: [String],
    testReports: [{
      name: String,
      url: String,
      publicId: String,
      issuedBy: String,
      issuedDate: Date,
      validUntil: Date
    }],
    warranty: {
      duration: Number, // in months
      terms: String
    }
  },
  
  // Delivery Information
  delivery: {
    isDeliverable: { type: Boolean, default: true },
    deliveryTime: {
      min: Number, // minimum delivery time in hours
      max: Number  // maximum delivery time in hours
    },
    deliveryCharges: {
      type: Number,
      default: 0,
      min: [0, 'Delivery charges cannot be negative']
    },
    freeDeliveryThreshold: Number,
    packagingCharges: {
      type: Number,
      default: 0,
      min: [0, 'Packaging charges cannot be negative']
    }
  },
  
  // SEO and Search
  tags: [String],
  searchKeywords: [String],
  
  // Performance Metrics
  metrics: {
    views: { type: Number, default: 0 },
    orders: { type: Number, default: 0 },
    rating: {
      average: { type: Number, default: 0, min: 0, max: 5 },
      count: { type: Number, default: 0 }
    },
    lastOrdered: Date
  },
  
  // Status
  isActive: {
    type: Boolean,
    default: true
  },
  isApproved: {
    type: Boolean,
    default: false
  },
  isFeatured: {
    type: Boolean,
    default: false
  },
  
  // Timestamps
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
materialSchema.index({ vendorId: 1 });
materialSchema.index({ category: 1 });
materialSchema.index({ name: 'text', description: 'text', tags: 'text' });
materialSchema.index({ 'pricing.basePrice': 1 });
materialSchema.index({ 'metrics.rating.average': -1 });
materialSchema.index({ isActive: 1, isApproved: 1 });
materialSchema.index({ createdAt: -1 });

// Virtual for available stock
materialSchema.virtual('availableStock').get(function() {
  return this.inventory.currentStock - this.inventory.reservedStock;
});

// Virtual for primary image
materialSchema.virtual('primaryImage').get(function() {
  const primary = this.images.find(img => img.isPrimary);
  return primary || this.images[0] || null;
});

// Virtual for final price (including tax)
materialSchema.virtual('finalPrice').get(function() {
  const basePrice = this.pricing.basePrice;
  const gstAmount = (basePrice * this.pricing.tax.gst) / 100;
  const otherTax = this.pricing.tax.other || 0;
  return basePrice + gstAmount + otherTax;
});

// Virtual for stock status
materialSchema.virtual('stockStatus').get(function() {
  const available = this.availableStock;
  if (available <= 0) return 'out_of_stock';
  if (available <= this.inventory.lowStockThreshold) return 'low_stock';
  return 'in_stock';
});

// Method to check if material is in stock
materialSchema.methods.isInStock = function(quantity = 1) {
  return this.availableStock >= quantity;
};

// Method to reserve stock
materialSchema.methods.reserveStock = function(quantity) {
  if (!this.isInStock(quantity)) {
    throw new Error('Insufficient stock available');
  }
  this.inventory.reservedStock += quantity;
  return this.save();
};

// Method to release reserved stock
materialSchema.methods.releaseStock = function(quantity) {
  this.inventory.reservedStock = Math.max(0, this.inventory.reservedStock - quantity);
  return this.save();
};

// Method to update stock
materialSchema.methods.updateStock = function(quantity, operation = 'subtract') {
  if (operation === 'subtract') {
    this.inventory.currentStock = Math.max(0, this.inventory.currentStock - quantity);
    this.inventory.reservedStock = Math.max(0, this.inventory.reservedStock - quantity);
  } else if (operation === 'add') {
    this.inventory.currentStock += quantity;
  }
  return this.save();
};

// Method to get bulk price
materialSchema.methods.getBulkPrice = function(quantity) {
  const bulkPricing = this.pricing.bulkPricing
    .filter(bp => quantity >= bp.minQuantity && (!bp.maxQuantity || quantity <= bp.maxQuantity))
    .sort((a, b) => b.minQuantity - a.minQuantity)[0];
  
  if (bulkPricing) {
    return bulkPricing.pricePerUnit || (this.pricing.basePrice * (1 - bulkPricing.discount / 100));
  }
  
  return this.pricing.basePrice;
};

// Method to update rating
materialSchema.methods.updateRating = function(newRating) {
  const totalRating = (this.metrics.rating.average * this.metrics.rating.count) + newRating;
  this.metrics.rating.count += 1;
  this.metrics.rating.average = totalRating / this.metrics.rating.count;
  return this.save();
};

// Static method to search materials
materialSchema.statics.searchMaterials = function(query, filters = {}) {
  const searchQuery = {
    isActive: true,
    isApproved: true,
    ...filters
  };
  
  if (query) {
    searchQuery.$text = { $search: query };
  }
  
  return this.find(searchQuery)
    .populate('vendorId', 'businessName rating location')
    .sort({ score: { $meta: 'textScore' }, 'metrics.rating.average': -1 });
};

module.exports = mongoose.model('Material', materialSchema);
