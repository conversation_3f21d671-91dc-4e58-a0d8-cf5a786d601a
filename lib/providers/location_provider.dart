import 'package:flutter/material.dart';
import '../models/location_model.dart';

class LocationProvider extends ChangeNotifier {
  LocationModel? _currentLocation;
  String? _currentAddress;
  bool _isLoading = false;
  String? _errorMessage;
  List<LocationModel> _savedLocations = [];

  // Getters
  LocationModel? get currentLocation => _currentLocation;
  String? get currentAddress => _currentAddress;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  List<LocationModel> get savedLocations => _savedLocations;

  // Get current location
  Future<void> getCurrentLocation() async {
    _setLoading(true);
    _clearError();

    try {
      // Simulate location detection
      await Future.delayed(const Duration(seconds: 2));
      
      // Mock location data for Gurgaon
      _currentLocation = LocationModel(
        latitude: 28.4595,
        longitude: 77.0266,
        address: 'Sector 15, Gurgaon, Haryana',
        city: 'Gurgaon',
        state: 'Haryana',
        pincode: '122001',
        landmark: 'Near City Center Mall',
      );
      
      _currentAddress = _currentLocation!.address;
      _setLoading(false);
    } catch (e) {
      _setError('Failed to get current location: $e');
      _setLoading(false);
    }
  }

  // Set location manually
  void setLocation(LocationModel location) {
    _currentLocation = location;
    _currentAddress = location.address;
    notifyListeners();
  }

  // Add saved location
  void addSavedLocation(LocationModel location) {
    if (!_savedLocations.any((loc) => 
        loc.latitude == location.latitude && 
        loc.longitude == location.longitude)) {
      _savedLocations.add(location);
      notifyListeners();
    }
  }

  // Remove saved location
  void removeSavedLocation(LocationModel location) {
    _savedLocations.removeWhere((loc) => 
        loc.latitude == location.latitude && 
        loc.longitude == location.longitude);
    notifyListeners();
  }

  // Calculate distance between two locations
  double calculateDistance(LocationModel from, LocationModel to) {
    // Simplified distance calculation (in km)
    // In real app, use proper geolocation formulas
    const double earthRadius = 6371; // Earth's radius in km
    
    double lat1Rad = from.latitude * (3.14159 / 180);
    double lat2Rad = to.latitude * (3.14159 / 180);
    double deltaLatRad = (to.latitude - from.latitude) * (3.14159 / 180);
    double deltaLonRad = (to.longitude - from.longitude) * (3.14159 / 180);

    double a = (deltaLatRad / 2).abs() * (deltaLatRad / 2).abs() +
        (lat1Rad).abs() * (lat2Rad).abs() *
        (deltaLonRad / 2).abs() * (deltaLonRad / 2).abs();
    
    double c = 2 * (a.abs()).abs();
    
    return earthRadius * c;
  }

  // Get locations within radius
  List<T> getLocationsWithinRadius<T extends LocationModel>(
    List<T> locations,
    LocationModel center,
    double radiusKm,
  ) {
    return locations.where((location) {
      double distance = calculateDistance(center, location);
      return distance <= radiusKm;
    }).toList();
  }

  // Search locations by address
  Future<List<LocationModel>> searchLocations(String query) async {
    _setLoading(true);
    _clearError();

    try {
      await Future.delayed(const Duration(milliseconds: 500));
      
      // Mock search results
      List<LocationModel> searchResults = [
        LocationModel(
          latitude: 28.4595,
          longitude: 77.0266,
          address: 'Sector 15, Gurgaon, Haryana',
          city: 'Gurgaon',
          state: 'Haryana',
          pincode: '122001',
          landmark: 'Near City Center Mall',
        ),
        LocationModel(
          latitude: 28.4089,
          longitude: 77.0178,
          address: 'Cyber City, Gurgaon, Haryana',
          city: 'Gurgaon',
          state: 'Haryana',
          pincode: '122002',
          landmark: 'DLF Cyber Hub',
        ),
        LocationModel(
          latitude: 28.4744,
          longitude: 77.0266,
          address: 'MG Road, Gurgaon, Haryana',
          city: 'Gurgaon',
          state: 'Haryana',
          pincode: '122001',
          landmark: 'Near Metro Station',
        ),
      ];

      // Filter results based on query
      if (query.isNotEmpty) {
        searchResults = searchResults.where((location) =>
            location.address.toLowerCase().contains(query.toLowerCase()) ||
            location.city.toLowerCase().contains(query.toLowerCase()) ||
            location.landmark?.toLowerCase().contains(query.toLowerCase()) == true
        ).toList();
      }

      _setLoading(false);
      return searchResults;
    } catch (e) {
      _setError('Failed to search locations: $e');
      _setLoading(false);
      return [];
    }
  }

  // Get delivery areas for a vendor
  List<String> getDeliveryAreas(LocationModel vendorLocation, double deliveryRadius) {
    // Mock delivery areas based on vendor location
    return [
      'Sector 15, Gurgaon',
      'Sector 14, Gurgaon',
      'Sector 16, Gurgaon',
      'DLF Phase 1',
      'DLF Phase 2',
      'Cyber City',
      'MG Road',
      'Golf Course Road',
    ];
  }

  // Check if location is within delivery area
  bool isWithinDeliveryArea(LocationModel userLocation, LocationModel vendorLocation, double deliveryRadius) {
    double distance = calculateDistance(userLocation, vendorLocation);
    return distance <= deliveryRadius;
  }

  // Get estimated delivery time based on distance
  String getEstimatedDeliveryTime(double distanceKm) {
    if (distanceKm <= 2) {
      return '30-45 mins';
    } else if (distanceKm <= 5) {
      return '45-60 mins';
    } else if (distanceKm <= 10) {
      return '1-2 hours';
    } else {
      return '2-4 hours';
    }
  }

  // Initialize with sample saved locations
  void initializeSampleLocations() {
    _savedLocations = [
      LocationModel(
        latitude: 28.4595,
        longitude: 77.0266,
        address: 'Home - Sector 15, Gurgaon',
        city: 'Gurgaon',
        state: 'Haryana',
        pincode: '122001',
        landmark: 'Near City Center Mall',
      ),
      LocationModel(
        latitude: 28.4089,
        longitude: 77.0178,
        address: 'Office - Cyber City, Gurgaon',
        city: 'Gurgaon',
        state: 'Haryana',
        pincode: '122002',
        landmark: 'DLF Cyber Hub',
      ),
    ];
    notifyListeners();
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Clear all data
  void clear() {
    _currentLocation = null;
    _currentAddress = null;
    _savedLocations.clear();
    _errorMessage = null;
    _isLoading = false;
    notifyListeners();
  }
}
