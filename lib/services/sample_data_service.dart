import '../models/inventory_model.dart';
import '../models/user_model.dart';
import '../models/requirement_model.dart';
import '../models/bid_model.dart';
import '../models/order_model.dart';
import '../models/cart_model.dart';
import '../models/notification_model.dart';

class SampleDataService {
  // Sample vendors
  static List<UserModel> getSampleVendors() {
    return [
      UserModel(
        id: 'vendor_1',
        uid: 'vendor_1',
        name: '<PERSON>esh Building Materials',
        email: '<EMAIL>',
        phone: '+91 9876543210',
        role: 'vendor',
        status: 'active',
        location: {
          'city': 'Mumbai',
          'state': 'Maharashtra',
          'pincode': '400001',
          'address': 'Shop 15, Building Materials Market, Dadar'
        },
        rating: 4.5,
        totalOrders: 150,
        completedOrders: 142,
        createdAt: DateTime.now().subtract(const Duration(days: 365)),
        businessName: 'Rajesh Building Materials',
        isVerified: true,
      ),
      UserModel(
        id: 'vendor_2',
        uid: 'vendor_2',
        name: 'Sharma Construction Supplies',
        email: '<EMAIL>',
        phone: '+91 9876543211',
        role: 'vendor',
        status: 'active',
        location: {
          'city': 'Delhi',
          'state': 'Delhi',
          'pincode': '110001',
          'address': 'Block A, Construction Market, Karol Bagh'
        },
        rating: 4.2,
        totalOrders: 89,
        completedOrders: 85,
        createdAt: DateTime.now().subtract(const Duration(days: 200)),
        businessName: 'Sharma Construction Supplies',
        isVerified: true,
      ),
      UserModel(
        id: 'vendor_3',
        uid: 'vendor_3',
        name: 'Gupta Steel & Cement',
        email: '<EMAIL>',
        phone: '+91 9876543212',
        role: 'vendor',
        status: 'active',
        location: {
          'city': 'Pune',
          'state': 'Maharashtra',
          'pincode': '411001',
          'address': 'Warehouse 7, Industrial Area, Pimpri'
        },
        rating: 4.7,
        totalOrders: 203,
        completedOrders: 198,
        createdAt: DateTime.now().subtract(const Duration(days: 500)),
        businessName: 'Gupta Steel & Cement',
        isVerified: true,
      ),
    ];
  }

  // Sample inventory items
  static List<InventoryModel> getInventoryItems() {
    final vendors = getSampleVendors();
    return [
      // Cement items
      InventoryModel(
        id: 'inv_1',
        vendorId: vendors[0].id,
        vendorName: vendors[0].businessName!,
        materialType: 'cement',
        description: 'ACC Cement - Grade 53',
        pricePerUnit: 350.0,
        quantity: 500,
        unit: 'bags',
        inStock: true,
        photoUrls: [],
        status: 'active',
        vendorRating: vendors[0].rating,
        minOrderQuantity: 10,
        specifications: {
          'brand': 'ACC',
          'grade': '53',
          'weight': '50kg per bag'
        },
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      ),
      InventoryModel(
        id: 'inv_2',
        vendorId: vendors[1].id,
        vendorName: vendors[1].businessName!,
        materialType: 'cement',
        description: 'UltraTech Cement - Grade 43',
        pricePerUnit: 320.0,
        quantity: 300,
        unit: 'bags',
        inStock: true,
        photoUrls: [],
        status: 'active',
        vendorRating: vendors[1].rating,
        minOrderQuantity: 5,
        specifications: {
          'brand': 'UltraTech',
          'grade': '43',
          'weight': '50kg per bag'
        },
        createdAt: DateTime.now().subtract(const Duration(days: 25)),
        updatedAt: DateTime.now().subtract(const Duration(days: 2)),
      ),
      
      // Steel items
      InventoryModel(
        id: 'inv_3',
        vendorId: vendors[2].id,
        vendorName: vendors[2].businessName!,
        materialType: 'steel',
        description: 'TMT Steel Bars - 12mm',
        pricePerUnit: 55.0,
        quantity: 1000,
        unit: 'kg',
        inStock: true,
        photoUrls: [],
        status: 'active',
        vendorRating: vendors[2].rating,
        minOrderQuantity: 100,
        specifications: {
          'diameter': '12mm',
          'grade': 'Fe500',
          'length': '12 meters'
        },
        createdAt: DateTime.now().subtract(const Duration(days: 20)),
        updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      ),
      InventoryModel(
        id: 'inv_4',
        vendorId: vendors[0].id,
        vendorName: vendors[0].businessName!,
        materialType: 'steel',
        description: 'TMT Steel Bars - 16mm',
        pricePerUnit: 58.0,
        quantity: 800,
        unit: 'kg',
        inStock: true,
        photoUrls: [],
        status: 'active',
        vendorRating: vendors[0].rating,
        minOrderQuantity: 100,
        specifications: {
          'diameter': '16mm',
          'grade': 'Fe500',
          'length': '12 meters'
        },
        createdAt: DateTime.now().subtract(const Duration(days: 18)),
        updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      ),

      // Bricks
      InventoryModel(
        id: 'inv_5',
        vendorId: vendors[1].id,
        vendorName: vendors[1].businessName!,
        materialType: 'bricks',
        description: 'Red Clay Bricks - Standard',
        pricePerUnit: 8.0,
        quantity: 5000,
        unit: 'pieces',
        inStock: true,
        photoUrls: [],
        status: 'active',
        vendorRating: vendors[1].rating,
        minOrderQuantity: 100,
        specifications: {
          'type': 'Red Clay',
          'size': '230x110x70mm',
          'strength': 'Class A'
        },
        createdAt: DateTime.now().subtract(const Duration(days: 15)),
        updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      ),

      // Sand
      InventoryModel(
        id: 'inv_6',
        vendorId: vendors[2].id,
        vendorName: vendors[2].businessName!,
        materialType: 'sand',
        description: 'River Sand - Fine Grade',
        pricePerUnit: 1200.0,
        quantity: 50,
        unit: 'cubic meters',
        inStock: true,
        photoUrls: [],
        status: 'active',
        vendorRating: vendors[2].rating,
        minOrderQuantity: 5,
        specifications: {
          'type': 'River Sand',
          'grade': 'Fine',
          'moisture': 'Less than 5%'
        },
        createdAt: DateTime.now().subtract(const Duration(days: 10)),
        updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      ),

      // Aggregates
      InventoryModel(
        id: 'inv_7',
        vendorId: vendors[0].id,
        vendorName: vendors[0].businessName!,
        materialType: 'aggregates',
        description: '20mm Crushed Stone',
        pricePerUnit: 800.0,
        quantity: 100,
        unit: 'cubic meters',
        inStock: true,
        photoUrls: [],
        status: 'active',
        vendorRating: vendors[0].rating,
        minOrderQuantity: 10,
        specifications: {
          'size': '20mm',
          'type': 'Crushed Stone',
          'grade': 'Premium'
        },
        createdAt: DateTime.now().subtract(const Duration(days: 8)),
        updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      ),

      // Low stock item
      InventoryModel(
        id: 'inv_8',
        vendorId: vendors[1].id,
        vendorName: vendors[1].businessName!,
        materialType: 'cement',
        description: 'Ambuja Cement - Grade 53',
        pricePerUnit: 340.0,
        quantity: 5, // Low stock
        unit: 'bags',
        inStock: true,
        photoUrls: [],
        status: 'active',
        vendorRating: vendors[1].rating,
        minOrderQuantity: 5,
        specifications: {
          'brand': 'Ambuja',
          'grade': '53',
          'weight': '50kg per bag'
        },
        createdAt: DateTime.now().subtract(const Duration(days: 5)),
        updatedAt: DateTime.now().subtract(const Duration(hours: 2)),
      ),

      // Out of stock item
      InventoryModel(
        id: 'inv_9',
        vendorId: vendors[2].id,
        vendorName: vendors[2].businessName!,
        materialType: 'steel',
        description: 'TMT Steel Bars - 8mm',
        pricePerUnit: 52.0,
        quantity: 0, // Out of stock
        unit: 'kg',
        inStock: false,
        photoUrls: [],
        status: 'active',
        vendorRating: vendors[2].rating,
        minOrderQuantity: 100,
        specifications: {
          'diameter': '8mm',
          'grade': 'Fe500',
          'length': '12 meters'
        },
        createdAt: DateTime.now().subtract(const Duration(days: 3)),
        updatedAt: DateTime.now().subtract(const Duration(hours: 1)),
      ),
    ];
  }

  // Get material categories
  static List<Map<String, dynamic>> getMaterialCategories() {
    return [
      {
        'id': 'cement',
        'name': 'Cement',
        'name_hi': 'सीमेंट',
        'icon': 'construction',
        'color': 0xFFFF9800,
      },
      {
        'id': 'steel',
        'name': 'Steel',
        'name_hi': 'स्टील',
        'icon': 'hardware',
        'color': 0xFF607D8B,
      },
      {
        'id': 'bricks',
        'name': 'Bricks',
        'name_hi': 'ईंट',
        'icon': 'view_module',
        'color': 0xFFD32F2F,
      },
      {
        'id': 'sand',
        'name': 'Sand',
        'name_hi': 'रेत',
        'icon': 'grain',
        'color': 0xFFFFC107,
      },
      {
        'id': 'aggregates',
        'name': 'Aggregates',
        'name_hi': 'एग्रीगेट',
        'icon': 'scatter_plot',
        'color': 0xFF795548,
      },
    ];
  }
}
