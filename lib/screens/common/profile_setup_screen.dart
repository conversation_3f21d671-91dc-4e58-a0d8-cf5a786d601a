import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../providers/language_provider.dart';
import '../../utils/app_colors.dart';
import '../../utils/constants.dart';
import '../../widgets/custom_button.dart';
import '../../widgets/custom_text_field.dart';
import '../../routes/app_routes.dart';

class ProfileSetupScreen extends StatefulWidget {
  final String phoneNumber;
  final String userRole;

  const ProfileSetupScreen({
    Key? key,
    required this.phoneNumber,
    required this.userRole,
  }) : super(key: key);

  @override
  State<ProfileSetupScreen> createState() => _ProfileSetupScreenState();
}

class _ProfileSetupScreenState extends State<ProfileSetupScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _addressController = TextEditingController();
  final _cityController = TextEditingController();
  final _stateController = TextEditingController();
  final _pincodeController = TextEditingController();
  
  String? _selectedUserType;
  int _currentStep = 0;

  @override
  void initState() {
    super.initState();
    _selectedUserType = widget.userRole;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _addressController.dispose();
    _cityController.dispose();
    _stateController.dispose();
    _pincodeController.dispose();
    super.dispose();
  }

  Future<void> _createProfile() async {
    if (!_formKey.currentState!.validate() || _selectedUserType == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please fill all required fields'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    
    final success = await authProvider.createUserProfile(
      name: _nameController.text.trim(),
      userType: _selectedUserType!,
      phoneNumber: widget.phoneNumber,
      email: _emailController.text.trim().isEmpty ? null : _emailController.text.trim(),
      address: _addressController.text.trim().isEmpty ? null : _addressController.text.trim(),
      city: _cityController.text.trim().isEmpty ? null : _cityController.text.trim(),
      state: _stateController.text.trim().isEmpty ? null : _stateController.text.trim(),
      pincode: _pincodeController.text.trim().isEmpty ? null : _pincodeController.text.trim(),
    );

    if (success) {
      _navigateToDashboard();
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(authProvider.errorMessage ?? 'Failed to create profile'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  void _navigateToDashboard() {
    if (_selectedUserType != null) {
      AppRoutes.navigateToDashboard(context, _selectedUserType!);
    }
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context);
    
    return Scaffold(
      appBar: AppBar(
        title: Text(languageProvider.getText('profile')),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: Form(
        key: _formKey,
        child: Stepper(
          currentStep: _currentStep,
          onStepTapped: (step) {
            setState(() {
              _currentStep = step;
            });
          },
          controlsBuilder: (context, details) {
            return Row(
              children: [
                if (details.stepIndex < 2)
                  CustomButton(
                    text: languageProvider.getText('next'),
                    onPressed: () {
                      if (_validateCurrentStep()) {
                        setState(() {
                          _currentStep++;
                        });
                      }
                    },
                  ),
                if (details.stepIndex == 2)
                  Consumer<AuthProvider>(
                    builder: (context, authProvider, child) {
                      return CustomButton(
                        text: languageProvider.getText('submit'),
                        onPressed: authProvider.isLoading ? null : _createProfile,
                        isLoading: authProvider.isLoading,
                      );
                    },
                  ),
                const SizedBox(width: 16),
                if (details.stepIndex > 0)
                  TextButton(
                    onPressed: () {
                      setState(() {
                        _currentStep--;
                      });
                    },
                    child: Text(languageProvider.getText('back')),
                  ),
              ],
            );
          },
          steps: [
            // Step 1: User Type Selection
            Step(
              title: Text(languageProvider.getText('select_user_type')),
              content: Column(
                children: [
                  _buildUserTypeCard(
                    type: AppConstants.userTypeBuilder,
                    title: languageProvider.getText('builder'),
                    description: 'Post requirements and get bids from vendors',
                    icon: Icons.business,
                    color: AppColors.builderColor,
                  ),
                  const SizedBox(height: 16),
                  _buildUserTypeCard(
                    type: AppConstants.userTypeMistri,
                    title: languageProvider.getText('mistri'),
                    description: 'Order materials instantly for your projects',
                    icon: Icons.construction_outlined,
                    color: AppColors.mistriColor,
                  ),
                  const SizedBox(height: 16),
                  _buildUserTypeCard(
                    type: AppConstants.userTypeVendor,
                    title: languageProvider.getText('vendor'),
                    description: 'Sell materials and bid on requirements',
                    icon: Icons.store,
                    color: AppColors.vendorColor,
                  ),
                ],
              ),
              isActive: _currentStep >= 0,
              state: _currentStep > 0 ? StepState.complete : StepState.indexed,
            ),
            
            // Step 2: Basic Information
            Step(
              title: Text('Basic Information'),
              content: Column(
                children: [
                  CustomTextField(
                    controller: _nameController,
                    label: languageProvider.getText('name'),
                    hint: 'Enter your full name',
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Name is required';
                      }
                      return null;
                    },
                    prefixIcon: const Icon(Icons.person),
                  ),
                  const SizedBox(height: 16),
                  CustomTextField(
                    controller: _emailController,
                    label: languageProvider.getText('email'),
                    hint: 'Enter your email (optional)',
                    keyboardType: TextInputType.emailAddress,
                    prefixIcon: const Icon(Icons.email),
                  ),
                  const SizedBox(height: 16),
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: AppColors.greyLight,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: [
                        const Icon(Icons.phone, color: AppColors.primary),
                        const SizedBox(width: 12),
                        Text(
                          widget.phoneNumber,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              isActive: _currentStep >= 1,
              state: _currentStep > 1 ? StepState.complete : 
                     _currentStep == 1 ? StepState.indexed : StepState.disabled,
            ),
            
            // Step 3: Address Information
            Step(
              title: Text('Address Information'),
              content: Column(
                children: [
                  CustomTextField(
                    controller: _addressController,
                    label: languageProvider.getText('address'),
                    hint: 'Enter your address (optional)',
                    maxLines: 2,
                    prefixIcon: const Icon(Icons.location_on),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: CustomTextField(
                          controller: _cityController,
                          label: languageProvider.getText('city'),
                          hint: 'City',
                          prefixIcon: const Icon(Icons.location_city),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: CustomTextField(
                          controller: _stateController,
                          label: languageProvider.getText('state'),
                          hint: 'State',
                          prefixIcon: const Icon(Icons.map),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  CustomTextField(
                    controller: _pincodeController,
                    label: languageProvider.getText('pincode'),
                    hint: 'Enter pincode',
                    keyboardType: TextInputType.number,
                    prefixIcon: const Icon(Icons.pin_drop),
                  ),
                ],
              ),
              isActive: _currentStep >= 2,
              state: _currentStep == 2 ? StepState.indexed : StepState.disabled,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUserTypeCard({
    required String type,
    required String title,
    required String description,
    required IconData icon,
    required Color color,
  }) {
    final isSelected = _selectedUserType == type;
    
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedUserType = type;
        });
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected ? color.withOpacity(0.1) : Colors.white,
          border: Border.all(
            color: isSelected ? color : AppColors.border,
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: isSelected ? color : AppColors.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: const TextStyle(
                      fontSize: 14,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            if (isSelected)
              Icon(Icons.check_circle, color: color, size: 24),
          ],
        ),
      ),
    );
  }

  bool _validateCurrentStep() {
    switch (_currentStep) {
      case 0:
        return _selectedUserType != null;
      case 1:
        return _nameController.text.trim().isNotEmpty;
      case 2:
        return true; // Address is optional
      default:
        return false;
    }
  }
}
