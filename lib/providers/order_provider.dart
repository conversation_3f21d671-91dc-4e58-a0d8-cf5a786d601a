import 'package:flutter/material.dart';
// import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/order_model.dart';
import '../models/cart_model.dart';
import '../services/sample_data_service.dart';

class OrderProvider extends ChangeNotifier {
  // final FirebaseFirestore _firestore = FirebaseService.firestore;
  
  List<OrderModel> _orders = [];
  List<OrderModel> _vendorOrders = [];
  CartModel _cart = CartModel.empty();
  bool _isLoading = false;
  String? _errorMessage;
  OrderModel? _currentOrder;

  // Getters
  List<OrderModel> get orders => _orders;
  List<OrderModel> get vendorOrders => _vendorOrders;
  CartModel get cart => _cart;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  OrderModel? get currentOrder => _currentOrder;
  int get cartItemCount => _cart.items.length;
  double get cartTotal => _cart.totalAmount;

  // Cart Management
  void addToCart(CartItemModel item) {
    final existingIndex = _cart.items.indexWhere(
      (cartItem) => cartItem.inventoryId == item.inventoryId,
    );

    if (existingIndex != -1) {
      // Update quantity if item already exists
      _cart.items[existingIndex] = _cart.items[existingIndex].copyWith(
        quantity: _cart.items[existingIndex].quantity + item.quantity,
      );
    } else {
      // Add new item
      _cart.items.add(item);
    }

    _updateCartTotal();
    notifyListeners();
  }

  void removeFromCart(String inventoryId) {
    _cart.items.removeWhere((item) => item.inventoryId == inventoryId);
    _updateCartTotal();
    notifyListeners();
  }

  void updateCartItemQuantity(String inventoryId, int quantity) {
    if (quantity <= 0) {
      removeFromCart(inventoryId);
      return;
    }

    final index = _cart.items.indexWhere(
      (item) => item.inventoryId == inventoryId,
    );

    if (index != -1) {
      _cart.items[index] = _cart.items[index].copyWith(quantity: quantity);
      _updateCartTotal();
      notifyListeners();
    }
  }

  void clearCart() {
    _cart = CartModel.empty();
    notifyListeners();
  }

  void _updateCartTotal() {
    _cart = _cart.copyWith(
      totalAmount: _cart.items.fold(
        0.0,
        (total, item) => total + (item.price * item.quantity),
      ),
    );
  }

  // Order Management
  Future<bool> placeOrder({
    required String userId,
    required String deliveryAddress,
    required Map<String, dynamic> location,
    String? notes,
  }) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      if (_cart.items.isEmpty) {
        throw Exception('Cart is empty');
      }

      // Group items by vendor
      final vendorGroups = <String, List<CartItemModel>>{};
      for (final item in _cart.items) {
        if (!vendorGroups.containsKey(item.vendorId)) {
          vendorGroups[item.vendorId] = [];
        }
        vendorGroups[item.vendorId]!.add(item);
      }

      // Create separate orders for each vendor
      final batch = _firestore.batch();
      final orderIds = <String>[];

      for (final vendorEntry in vendorGroups.entries) {
        final vendorId = vendorEntry.key;
        final vendorItems = vendorEntry.value;
        
        final vendorTotal = vendorItems.fold(
          0.0,
          (total, item) => total + (item.price * item.quantity),
        );

        final order = OrderModel(
          id: '', // Will be set by Firestore
          userId: userId,
          vendorId: vendorId,
          vendorName: vendorItems.first.vendorName,
          items: vendorItems,
          totalAmount: vendorTotal,
          deliveryAddress: deliveryAddress,
          location: location,
          status: 'placed',
          paymentStatus: 'pending',
          createdAt: DateTime.now(),
          notes: notes,
        );

        final orderRef = _firestore.collection('orders').doc();
        batch.set(orderRef, order.toMap());
        orderIds.add(orderRef.id);

        // Update inventory quantities
        for (final item in vendorItems) {
          final inventoryRef = _firestore.collection('inventory').doc(item.inventoryId);
          batch.update(inventoryRef, {
            'quantity': FieldValue.increment(-item.quantity),
            'updated_at': FieldValue.serverTimestamp(),
          });
        }

        // Create notification for vendor
        final notificationRef = _firestore.collection('notifications').doc();
        batch.set(notificationRef, {
          'to_user_id': vendorId,
          'type': 'new_order',
          'title': 'New Order Received',
          'message': 'You have received a new order worth ₹${vendorTotal.toStringAsFixed(0)}',
          'data': {'order_id': orderRef.id},
          'created_at': FieldValue.serverTimestamp(),
          'read': false,
        });
      }

      await batch.commit();

      // Clear cart after successful order
      clearCart();

      // Load user orders to refresh the list
      await loadUserOrders(userId);

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _errorMessage = e.toString();
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  // Load orders for a user
  Future<void> loadUserOrders(String userId) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      final querySnapshot = await _firestore
          .collection('orders')
          .where('user_id', isEqualTo: userId)
          .orderBy('created_at', descending: true)
          .get();

      _orders = querySnapshot.docs
          .map((doc) => OrderModel.fromFirestore(doc))
          .toList();

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _errorMessage = e.toString();
      _isLoading = false;
      notifyListeners();
    }
  }

  // Load orders for a vendor
  Future<void> loadVendorOrders(String vendorId) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      final querySnapshot = await _firestore
          .collection('orders')
          .where('vendor_id', isEqualTo: vendorId)
          .orderBy('created_at', descending: true)
          .get();

      _vendorOrders = querySnapshot.docs
          .map((doc) => OrderModel.fromFirestore(doc))
          .toList();

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _errorMessage = e.toString();
      _isLoading = false;
      notifyListeners();
    }
  }

  // Update order status
  Future<bool> updateOrderStatus(String orderId, String status) async {
    try {
      await _firestore.collection('orders').doc(orderId).update({
        'status': status,
        'updated_at': FieldValue.serverTimestamp(),
      });

      // Update local orders
      _updateLocalOrderStatus(orderId, status);

      // Create notification for user
      final order = getOrderById(orderId);
      if (order != null) {
        await _firestore.collection('notifications').add({
          'to_user_id': order.userId,
          'type': 'order_update',
          'title': 'Order Status Updated',
          'message': 'Your order is now $status',
          'data': {'order_id': orderId},
          'created_at': FieldValue.serverTimestamp(),
          'read': false,
        });
      }

      notifyListeners();
      return true;
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Accept order (for vendors)
  Future<bool> acceptOrder(String orderId) async {
    return await updateOrderStatus(orderId, 'confirmed');
  }

  // Reject order (for vendors)
  Future<bool> rejectOrder(String orderId, String reason) async {
    try {
      await _firestore.collection('orders').doc(orderId).update({
        'status': 'cancelled',
        'cancellation_reason': reason,
        'updated_at': FieldValue.serverTimestamp(),
      });

      _updateLocalOrderStatus(orderId, 'cancelled');
      notifyListeners();
      return true;
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Cancel order (for users)
  Future<bool> cancelOrder(String orderId, String reason) async {
    try {
      final order = getOrderById(orderId);
      if (order == null) return false;

      // Only allow cancellation if order is not yet processed
      if (!['placed', 'confirmed'].contains(order.status)) {
        throw Exception('Order cannot be cancelled at this stage');
      }

      await _firestore.collection('orders').doc(orderId).update({
        'status': 'cancelled',
        'cancellation_reason': reason,
        'updated_at': FieldValue.serverTimestamp(),
      });

      // Restore inventory quantities
      final batch = _firestore.batch();
      for (final item in order.items) {
        final inventoryRef = _firestore.collection('inventory').doc(item.inventoryId);
        batch.update(inventoryRef, {
          'quantity': FieldValue.increment(item.quantity),
          'updated_at': FieldValue.serverTimestamp(),
        });
      }
      await batch.commit();

      _updateLocalOrderStatus(orderId, 'cancelled');
      notifyListeners();
      return true;
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Get order by ID
  OrderModel? getOrderById(String orderId) {
    try {
      return _orders.firstWhere((order) => order.id == orderId);
    } catch (e) {
      try {
        return _vendorOrders.firstWhere((order) => order.id == orderId);
      } catch (e) {
        return null;
      }
    }
  }

  // Update local order status
  void _updateLocalOrderStatus(String orderId, String status) {
    final orderIndex = _orders.indexWhere((order) => order.id == orderId);
    if (orderIndex != -1) {
      _orders[orderIndex] = _orders[orderIndex].copyWith(status: status);
    }

    final vendorOrderIndex = _vendorOrders.indexWhere((order) => order.id == orderId);
    if (vendorOrderIndex != -1) {
      _vendorOrders[vendorOrderIndex] = _vendorOrders[vendorOrderIndex].copyWith(status: status);
    }
  }

  // Get orders by status
  List<OrderModel> getOrdersByStatus(String status, {bool isVendor = false}) {
    final orderList = isVendor ? _vendorOrders : _orders;
    return orderList.where((order) => order.status == status).toList();
  }

  // Get order statistics for vendor
  Map<String, dynamic> getVendorOrderStats() {
    final totalOrders = _vendorOrders.length;
    final pendingOrders = _vendorOrders.where((o) => o.status == 'placed').length;
    final completedOrders = _vendorOrders.where((o) => o.status == 'delivered').length;
    final totalRevenue = _vendorOrders
        .where((o) => o.status == 'delivered')
        .fold(0.0, (sum, order) => sum + order.totalAmount);

    return {
      'totalOrders': totalOrders,
      'pendingOrders': pendingOrders,
      'completedOrders': completedOrders,
      'totalRevenue': totalRevenue,
    };
  }

  // Clear error
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Set current order for tracking
  void setCurrentOrder(OrderModel order) {
    _currentOrder = order;
    notifyListeners();
  }

  // Clear current order
  void clearCurrentOrder() {
    _currentOrder = null;
    notifyListeners();
  }
}
