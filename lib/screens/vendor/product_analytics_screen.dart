import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/product_provider.dart';
import '../../utils/app_colors.dart';

class ProductAnalyticsScreen extends StatefulWidget {
  const ProductAnalyticsScreen({Key? key}) : super(key: key);

  @override
  State<ProductAnalyticsScreen> createState() => _ProductAnalyticsScreenState();
}

class _ProductAnalyticsScreenState extends State<ProductAnalyticsScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Product Analytics'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Consumer<ProductProvider>(
        builder: (context, productProvider, child) {
          final products = productProvider.products;
          
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Overview Cards
                Row(
                  children: [
                    Expanded(
                      child: _buildMetricCard(
                        'Total Products',
                        products.length.toString(),
                        Icons.inventory_2,
                        AppColors.primary,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildMetricCard(
                        'Active Products',
                        products.where((p) => p.isAvailable).length.toString(),
                        Icons.visibility,
                        AppColors.success,
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 12),
                
                Row(
                  children: [
                    Expanded(
                      child: _buildMetricCard(
                        'Low Stock',
                        products.where((p) => p.stockQuantity <= 10).length.toString(),
                        Icons.warning,
                        AppColors.warning,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildMetricCard(
                        'Out of Stock',
                        products.where((p) => p.stockQuantity == 0).length.toString(),
                        Icons.error,
                        AppColors.error,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 24),

                // Top Performing Products
                const Text(
                  'Top Performing Products',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                
                Card(
                  child: Column(
                    children: products
                        .where((p) => p.rating >= 4.0)
                        .take(5)
                        .map((product) => ListTile(
                              leading: CircleAvatar(
                                backgroundColor: AppColors.primary.withOpacity(0.1),
                                child: Icon(
                                  Icons.star,
                                  color: AppColors.primary,
                                ),
                              ),
                              title: Text(product.name),
                              subtitle: Text('${product.category.toUpperCase()} • ₹${product.price}'),
                              trailing: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    Icons.star,
                                    size: 16,
                                    color: AppColors.warning,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    product.rating.toString(),
                                    style: const TextStyle(fontWeight: FontWeight.w600),
                                  ),
                                ],
                              ),
                            ))
                        .toList(),
                  ),
                ),

                const SizedBox(height: 24),

                // Category Distribution
                const Text(
                  'Category Distribution',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children: _getCategoryDistribution(products)
                          .entries
                          .map((entry) => Padding(
                                padding: const EdgeInsets.only(bottom: 12),
                                child: Row(
                                  children: [
                                    Container(
                                      width: 12,
                                      height: 12,
                                      decoration: BoxDecoration(
                                        color: _getCategoryColor(entry.key),
                                        shape: BoxShape.circle,
                                      ),
                                    ),
                                    const SizedBox(width: 12),
                                    Expanded(
                                      child: Text(
                                        entry.key.toUpperCase(),
                                        style: const TextStyle(fontWeight: FontWeight.w500),
                                      ),
                                    ),
                                    Text(
                                      '${entry.value} products',
                                      style: TextStyle(
                                        color: AppColors.textSecondary,
                                      ),
                                    ),
                                  ],
                                ),
                              ))
                          .toList(),
                    ),
                  ),
                ),

                const SizedBox(height: 24),

                // Stock Status
                const Text(
                  'Stock Status Overview',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children: [
                        _buildStockStatusRow(
                          'In Stock',
                          products.where((p) => p.stockQuantity > 10).length,
                          products.length,
                          AppColors.success,
                        ),
                        const SizedBox(height: 12),
                        _buildStockStatusRow(
                          'Low Stock',
                          products.where((p) => p.stockQuantity > 0 && p.stockQuantity <= 10).length,
                          products.length,
                          AppColors.warning,
                        ),
                        const SizedBox(height: 12),
                        _buildStockStatusRow(
                          'Out of Stock',
                          products.where((p) => p.stockQuantity == 0).length,
                          products.length,
                          AppColors.error,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildMetricCard(String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 24),
                const Spacer(),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(
                fontSize: 14,
                color: AppColors.textSecondary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStockStatusRow(String label, int count, int total, Color color) {
    final percentage = total > 0 ? (count / total) : 0.0;
    
    return Row(
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            label,
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
        ),
        Text(
          '$count (${(percentage * 100).toStringAsFixed(1)}%)',
          style: TextStyle(
            color: AppColors.textSecondary,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Map<String, int> _getCategoryDistribution(List products) {
    final Map<String, int> distribution = {};
    
    for (final product in products) {
      final category = product.category;
      distribution[category] = (distribution[category] ?? 0) + 1;
    }
    
    return distribution;
  }

  Color _getCategoryColor(String category) {
    switch (category.toLowerCase()) {
      case 'cement':
        return AppColors.primary;
      case 'bricks':
        return Colors.red.shade400;
      case 'steel':
        return Colors.grey.shade600;
      case 'sand':
        return Colors.orange.shade400;
      case 'paints':
        return Colors.blue.shade400;
      case 'tiles':
        return Colors.purple.shade400;
      default:
        return AppColors.textSecondary;
    }
  }
}
