# BuildBid Backend API

Production-ready backend for BuildBid construction marketplace - a Zomato-style platform for construction materials and services.

## 🚀 Features

- **Authentication**: JWT-based auth with OTP verification
- **Vendor Management**: Complete vendor profiles and inventory
- **Material Catalog**: Comprehensive material listings with search
- **Order Management**: End-to-end order processing with tracking
- **Service Booking**: Raj <PERSON> service booking and management
- **Real-time Updates**: Socket.IO for live order tracking
- **Push Notifications**: Firebase FCM integration
- **SMS Integration**: Twilio for OTP and notifications
- **Payment Gateway**: Razorpay integration
- **File Upload**: Cloudinary for image management
- **API Documentation**: Swagger/OpenAPI documentation
- **Rate Limiting**: Request throttling and security
- **Error Handling**: Comprehensive error management

## 🛠️ Tech Stack

- **Runtime**: Node.js 16+
- **Framework**: Express.js
- **Database**: MongoDB with Mongoose ODM
- **Cache**: Redis
- **Authentication**: JWT
- **Real-time**: Socket.IO
- **File Storage**: Cloudinary
- **SMS**: Twilio
- **Push Notifications**: Firebase FCM
- **Payments**: Razorpay
- **Documentation**: Swagger UI
- **Security**: Helmet, CORS, Rate Limiting

## 📋 Prerequisites

- Node.js 16 or higher
- MongoDB 4.4 or higher
- Redis 6 or higher
- Twilio account for SMS
- Firebase project for push notifications
- Cloudinary account for image storage
- Razorpay account for payments

## 🔧 Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd backend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   ```bash
   cp .env.example .env
   ```
   
   Fill in your environment variables in `.env`:
   ```env
   # Server
   PORT=3000
   NODE_ENV=production
   
   # Database
   MONGODB_URI=mongodb://localhost:27017/buildbid
   REDIS_URL=redis://localhost:6379
   
   # JWT
   JWT_SECRET=your_super_secret_jwt_key
   JWT_EXPIRE=7d
   
   # Twilio (SMS)
   TWILIO_ACCOUNT_SID=your_twilio_sid
   TWILIO_AUTH_TOKEN=your_twilio_token
   TWILIO_PHONE_NUMBER=+**********
   
   # Firebase (Push Notifications)
   FIREBASE_PROJECT_ID=your_project_id
   FIREBASE_PRIVATE_KEY=your_private_key
   FIREBASE_CLIENT_EMAIL=your_client_email
   
   # Cloudinary (Image Storage)
   CLOUDINARY_CLOUD_NAME=your_cloud_name
   CLOUDINARY_API_KEY=your_api_key
   CLOUDINARY_API_SECRET=your_api_secret
   
   # Razorpay (Payments)
   RAZORPAY_KEY_ID=your_key_id
   RAZORPAY_KEY_SECRET=your_key_secret
   ```

4. **Database Setup**
   ```bash
   # Start MongoDB and Redis
   mongod
   redis-server
   
   # Seed the database with sample data
   npm run seed
   ```

5. **Start the server**
   ```bash
   # Development
   npm run dev
   
   # Production
   npm start
   ```

## 📚 API Documentation

Once the server is running, visit:
- **Swagger UI**: http://localhost:3000/api-docs
- **Health Check**: http://localhost:3000/health

## 🔐 Authentication

The API uses JWT tokens for authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

### Authentication Flow

1. **Send OTP**: `POST /api/auth/send-otp`
2. **Verify OTP**: `POST /api/auth/verify-otp`
3. **Use Token**: Include in Authorization header for protected routes

## 📱 API Endpoints

### Authentication
- `POST /api/auth/send-otp` - Send OTP to phone
- `POST /api/auth/verify-otp` - Verify OTP and login
- `GET /api/auth/profile` - Get user profile
- `PUT /api/auth/profile` - Update user profile

### Vendors
- `GET /api/vendors` - Get all vendors with filters
- `GET /api/vendors/:id` - Get vendor by ID
- `POST /api/vendors` - Create vendor profile
- `PUT /api/vendors/:id` - Update vendor profile

### Materials
- `GET /api/materials` - Get materials with search/filters
- `GET /api/materials/:id` - Get material by ID
- `POST /api/materials` - Create new material
- `PUT /api/materials/:id` - Update material
- `DELETE /api/materials/:id` - Delete material

### Orders
- `POST /api/orders` - Create new order
- `GET /api/orders` - Get user's orders
- `GET /api/orders/:id` - Get order by ID
- `PUT /api/orders/:id/status` - Update order status
- `POST /api/orders/:id/verify-delivery` - Verify delivery with OTP

## 🔄 Real-time Features

The API supports real-time updates via Socket.IO:

### Events
- `track_order` - Track order status updates
- `location_update` - Delivery location updates
- `send_message` - Order chat messages

### Connection
```javascript
const socket = io('http://localhost:3000', {
  auth: {
    token: 'your-jwt-token'
  }
});
```

## 📊 Database Schema

### Collections
- **users** - User profiles and authentication
- **vendors** - Vendor business profiles
- **materials** - Material catalog
- **orders** - Order management
- **services** - Raj Mistri services
- **reviews** - Reviews and ratings

### Indexes
- Geospatial indexes for location-based queries
- Text indexes for search functionality
- Compound indexes for performance optimization

## 🚀 Deployment

### Docker Deployment
```bash
# Build image
docker build -t buildbid-backend .

# Run container
docker run -p 3000:3000 --env-file .env buildbid-backend
```

### Production Checklist
- [ ] Set `NODE_ENV=production`
- [ ] Configure MongoDB replica set
- [ ] Set up Redis cluster
- [ ] Configure SSL/TLS
- [ ] Set up monitoring (PM2, New Relic)
- [ ] Configure log aggregation
- [ ] Set up backup strategy
- [ ] Configure CDN for static assets

## 🔒 Security Features

- JWT token authentication
- Request rate limiting
- Input validation and sanitization
- CORS protection
- Helmet security headers
- MongoDB injection prevention
- File upload restrictions

## 📈 Monitoring

### Health Check
```bash
curl http://localhost:3000/health
```

### Metrics
- Request rate and response times
- Database connection status
- Redis connection status
- Error rates and types

## 🧪 Testing

```bash
# Run tests
npm test

# Run with coverage
npm run test:coverage
```

## 📝 Scripts

- `npm start` - Start production server
- `npm run dev` - Start development server with nodemon
- `npm run seed` - Seed database with sample data
- `npm run migrate` - Run database migrations
- `npm test` - Run test suite

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support, email <EMAIL> or create an issue in the repository.

---

**BuildBid Backend** - Production-ready API for construction marketplace 🏗️
