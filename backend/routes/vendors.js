const express = require('express');
const { body, query, validationResult } = require('express-validator');
const Vendor = require('../models/Vendor');
const User = require('../models/User');
const Material = require('../models/Material');
const { authenticateToken, authorize } = require('../middleware/auth');
const router = express.Router();

/**
 * @swagger
 * /api/vendors:
 *   get:
 *     summary: Get all vendors with filters
 *     tags: [Vendors]
 *     parameters:
 *       - in: query
 *         name: specialization
 *         schema:
 *           type: string
 *         description: Filter by specialization
 *       - in: query
 *         name: city
 *         schema:
 *           type: string
 *         description: Filter by city
 *       - in: query
 *         name: rating
 *         schema:
 *           type: number
 *         description: Minimum rating filter
 *       - in: query
 *         name: latitude
 *         schema:
 *           type: number
 *         description: User latitude for nearby search
 *       - in: query
 *         name: longitude
 *         schema:
 *           type: number
 *         description: User longitude for nearby search
 *       - in: query
 *         name: radius
 *         schema:
 *           type: number
 *         description: Search radius in km (default: 10)
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number (default: 1)
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Items per page (default: 20)
 *     responses:
 *       200:
 *         description: Vendors retrieved successfully
 */
router.get('/', [
  query('specialization').optional().isString(),
  query('city').optional().isString(),
  query('rating').optional().isFloat({ min: 0, max: 5 }),
  query('latitude').optional().isFloat(),
  query('longitude').optional().isFloat(),
  query('radius').optional().isFloat({ min: 1, max: 100 }),
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    // Demo data for when database is not available
    const demoVendors = [
      {
        _id: '1',
        businessName: 'Kumar Construction Materials',
        specializations: ['bricks', 'cement', 'sand'],
        rating: { average: 4.5, count: 127 },
        location: { city: 'Gurgaon', address: 'Sector 15, Gurgaon' },
        deliveryRadius: 15,
        minimumOrderValue: 500,
        isActive: true,
        isVerified: true
      },
      {
        _id: '2',
        businessName: 'Sharma Steel & Cement',
        specializations: ['steel', 'cement', 'tiles'],
        rating: { average: 4.8, count: 89 },
        location: { city: 'Gurgaon', address: 'Cyber City, Gurgaon' },
        deliveryRadius: 20,
        minimumOrderValue: 1000,
        isActive: true,
        isVerified: true
      }
    ];

    return res.json({
      success: true,
      data: {
        vendors: demoVendors,
        pagination: {
          currentPage: 1,
          totalPages: 1,
          totalItems: demoVendors.length,
          itemsPerPage: 20
        }
      }
    });

    const {
      specialization,
      city,
      rating,
      latitude,
      longitude,
      radius = 10,
      page = 1,
      limit = 20
    } = req.query;

    let vendors;
    let totalCount;

    // Build query
    const query = {
      isActive: true,
      isVerified: true,
      isApproved: true
    };

    if (specialization) {
      query.specializations = specialization;
    }

    if (rating) {
      query['rating.average'] = { $gte: parseFloat(rating) };
    }

    // If coordinates provided, search nearby
    if (latitude && longitude) {
      vendors = await Vendor.findNearby(
        parseFloat(longitude),
        parseFloat(latitude),
        parseFloat(radius),
        {
          limit: parseInt(limit),
          sort: { distance: 1, 'rating.average': -1 }
        }
      );
      totalCount = vendors.length;
    } else {
      // Regular search with pagination
      const skip = (parseInt(page) - 1) * parseInt(limit);
      
      if (city) {
        // Add city filter through user lookup
        vendors = await Vendor.aggregate([
          {
            $lookup: {
              from: 'users',
              localField: 'userId',
              foreignField: '_id',
              as: 'user'
            }
          },
          {
            $unwind: '$user'
          },
          {
            $match: {
              ...query,
              'user.location.city': new RegExp(city, 'i')
            }
          },
          {
            $sort: { 'rating.average': -1, createdAt: -1 }
          },
          {
            $skip: skip
          },
          {
            $limit: parseInt(limit)
          }
        ]);
      } else {
        vendors = await Vendor.find(query)
          .populate('userId', 'name phone location profileImage')
          .sort({ 'rating.average': -1, createdAt: -1 })
          .skip(skip)
          .limit(parseInt(limit));
      }

      totalCount = await Vendor.countDocuments(query);
    }

    res.json({
      success: true,
      data: {
        vendors,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(totalCount / parseInt(limit)),
          totalItems: totalCount,
          itemsPerPage: parseInt(limit)
        }
      }
    });
  } catch (error) {
    console.error('Get vendors error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get vendors'
    });
  }
});

/**
 * @swagger
 * /api/vendors/{id}:
 *   get:
 *     summary: Get vendor by ID
 *     tags: [Vendors]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Vendor ID
 *     responses:
 *       200:
 *         description: Vendor retrieved successfully
 *       404:
 *         description: Vendor not found
 */
router.get('/:id', async (req, res) => {
  try {
    const vendor = await Vendor.findById(req.params.id)
      .populate('userId', 'name phone location profileImage');

    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found'
      });
    }

    // Get vendor's materials
    const materials = await Material.find({
      vendorId: vendor._id,
      isActive: true,
      isApproved: true
    }).limit(20);

    res.json({
      success: true,
      data: {
        vendor,
        materials
      }
    });
  } catch (error) {
    console.error('Get vendor error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get vendor'
    });
  }
});

/**
 * @swagger
 * /api/vendors:
 *   post:
 *     summary: Create vendor profile
 *     tags: [Vendors]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - businessName
 *               - specializations
 *             properties:
 *               businessName:
 *                 type: string
 *               businessType:
 *                 type: string
 *                 enum: [individual, partnership, company, cooperative]
 *               specializations:
 *                 type: array
 *                 items:
 *                   type: string
 *               description:
 *                 type: string
 *               gstNumber:
 *                 type: string
 *               deliveryRadius:
 *                 type: number
 *               minimumOrderValue:
 *                 type: number
 *     responses:
 *       201:
 *         description: Vendor profile created successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 */
router.post('/', authenticateToken, [
  body('businessName')
    .trim()
    .isLength({ min: 2, max: 200 })
    .withMessage('Business name must be between 2 and 200 characters'),
  body('businessType')
    .optional()
    .isIn(['individual', 'partnership', 'company', 'cooperative'])
    .withMessage('Invalid business type'),
  body('specializations')
    .isArray({ min: 1 })
    .withMessage('At least one specialization is required'),
  body('description')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Description cannot exceed 1000 characters'),
  body('gstNumber')
    .optional()
    .matches(/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/)
    .withMessage('Invalid GST number format'),
  body('deliveryRadius')
    .optional()
    .isFloat({ min: 1, max: 100 })
    .withMessage('Delivery radius must be between 1 and 100 km'),
  body('minimumOrderValue')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Minimum order value cannot be negative')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    // Check if vendor profile already exists
    const existingVendor = await Vendor.findOne({ userId: req.user.userId });
    if (existingVendor) {
      return res.status(400).json({
        success: false,
        message: 'Vendor profile already exists'
      });
    }

    // Create vendor profile
    const vendor = new Vendor({
      userId: req.user.userId,
      ...req.body
    });

    await vendor.save();

    // Update user role to vendor
    await User.findByIdAndUpdate(req.user.userId, { role: 'vendor' });

    res.status(201).json({
      success: true,
      message: 'Vendor profile created successfully',
      data: vendor
    });
  } catch (error) {
    console.error('Create vendor error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create vendor profile'
    });
  }
});

/**
 * @swagger
 * /api/vendors/{id}:
 *   put:
 *     summary: Update vendor profile
 *     tags: [Vendors]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Vendor ID
 *     responses:
 *       200:
 *         description: Vendor profile updated successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 *       404:
 *         description: Vendor not found
 */
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const vendor = await Vendor.findById(req.params.id);
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found'
      });
    }

    // Check if user owns this vendor profile
    if (vendor.userId.toString() !== req.user.userId) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    // Update vendor
    const allowedFields = [
      'businessName', 'businessType', 'specializations', 'description',
      'gstNumber', 'deliveryRadius', 'minimumOrderValue', 'deliveryCharges',
      'freeDeliveryThreshold', 'businessHours', 'deliveryAreas'
    ];

    allowedFields.forEach(field => {
      if (req.body[field] !== undefined) {
        vendor[field] = req.body[field];
      }
    });

    await vendor.save();

    res.json({
      success: true,
      message: 'Vendor profile updated successfully',
      data: vendor
    });
  } catch (error) {
    console.error('Update vendor error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update vendor profile'
    });
  }
});

/**
 * @swagger
 * /api/vendors/my-profile:
 *   get:
 *     summary: Get current user's vendor profile
 *     tags: [Vendors]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Vendor profile retrieved successfully
 *       404:
 *         description: Vendor profile not found
 */
router.get('/my/profile', authenticateToken, async (req, res) => {
  try {
    const vendor = await Vendor.findOne({ userId: req.user.userId })
      .populate('userId', 'name phone location profileImage');

    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor profile not found'
      });
    }

    res.json({
      success: true,
      data: vendor
    });
  } catch (error) {
    console.error('Get my vendor profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get vendor profile'
    });
  }
});

module.exports = router;
