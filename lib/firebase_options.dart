// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBuildBidWebApiKey123456789',
    appId: '1:123456789:web:buildbid123456789',
    messagingSenderId: '123456789',
    projectId: 'buildbid-production',
    authDomain: 'buildbid-production.firebaseapp.com',
    storageBucket: 'buildbid-production.appspot.com',
    measurementId: 'G-BUILDBID123',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBuildBidAndroidApiKey123456789',
    appId: '1:123456789:android:buildbid123456789',
    messagingSenderId: '123456789',
    projectId: 'buildbid-production',
    storageBucket: 'buildbid-production.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBuildBidIOSApiKey123456789',
    appId: '1:123456789:ios:buildbid123456789',
    messagingSenderId: '123456789',
    projectId: 'buildbid-production',
    storageBucket: 'buildbid-production.appspot.com',
    iosBundleId: 'com.buildbid.app',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyBuildBidMacOSApiKey123456789',
    appId: '1:123456789:macos:buildbid123456789',
    messagingSenderId: '123456789',
    projectId: 'buildbid-production',
    storageBucket: 'buildbid-production.appspot.com',
    iosBundleId: 'com.buildbid.app',
  );
}
