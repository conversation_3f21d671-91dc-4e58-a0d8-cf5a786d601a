// import 'package:cloud_firestore/cloud_firestore.dart';

class NotificationModel {
  final String id;
  final String toUserId;
  final String type; // new_bid, bid_accepted, order_update, etc.
  final String title;
  final String message;
  final Map<String, dynamic> data;
  final DateTime createdAt;
  final bool read;
  final DateTime? readAt;

  NotificationModel({
    required this.id,
    required this.toUserId,
    required this.type,
    required this.title,
    required this.message,
    this.data = const {},
    required this.createdAt,
    this.read = false,
    this.readAt,
  });

  // factory NotificationModel.fromFirestore(DocumentSnapshot doc) {
  //   final data = doc.data() as Map<String, dynamic>;
  //   return NotificationModel(
  //     id: doc.id,
  //     toUserId: data['to_user_id'] ?? '',
  //     type: data['type'] ?? '',
  //     title: data['title'] ?? '',
  //     message: data['message'] ?? '',
  //     data: data['data'] ?? {},
  //     createdAt: (data['created_at'] as Timestamp?)?.toDate() ?? DateTime.now(),
  //     read: data['read'] ?? false,
  //     readAt: (data['read_at'] as Timestamp?)?.toDate(),
  //   );
  // }

  factory NotificationModel.fromMap(Map<String, dynamic> data, String id) {
    return NotificationModel(
      id: id,
      toUserId: data['to_user_id'] ?? '',
      type: data['type'] ?? '',
      title: data['title'] ?? '',
      message: data['message'] ?? '',
      data: data['data'] ?? {},
      createdAt: data['created_at'] is String
          ? DateTime.parse(data['created_at'])
          : DateTime.now(),
      read: data['read'] ?? false,
      readAt: data['read_at'] is String
          ? DateTime.parse(data['read_at'])
          : null,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'to_user_id': toUserId,
      'type': type,
      'title': title,
      'message': message,
      'data': data,
      'created_at': createdAt.toIso8601String(),
      'read': read,
      'read_at': readAt?.toIso8601String(),
    };
  }

  NotificationModel copyWith({
    String? toUserId,
    String? type,
    String? title,
    String? message,
    Map<String, dynamic>? data,
    DateTime? createdAt,
    bool? read,
    DateTime? readAt,
  }) {
    return NotificationModel(
      id: id,
      toUserId: toUserId ?? this.toUserId,
      type: type ?? this.type,
      title: title ?? this.title,
      message: message ?? this.message,
      data: data ?? this.data,
      createdAt: createdAt ?? this.createdAt,
      read: read ?? this.read,
      readAt: readAt ?? this.readAt,
    );
  }

  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  String get formattedDate {
    return '${createdAt.day}/${createdAt.month}/${createdAt.year}';
  }

  String get formattedTime {
    return '${createdAt.hour.toString().padLeft(2, '0')}:${createdAt.minute.toString().padLeft(2, '0')}';
  }

  String get typeDisplay {
    switch (type) {
      case 'new_bid':
        return 'New Bid';
      case 'bid_accepted':
        return 'Bid Accepted';
      case 'bid_rejected':
        return 'Bid Rejected';
      case 'new_order':
        return 'New Order';
      case 'order_update':
        return 'Order Update';
      case 'order_confirmed':
        return 'Order Confirmed';
      case 'order_shipped':
        return 'Order Shipped';
      case 'order_delivered':
        return 'Order Delivered';
      case 'order_cancelled':
        return 'Order Cancelled';
      case 'requirement_update':
        return 'Requirement Update';
      case 'payment_success':
        return 'Payment Success';
      case 'payment_failed':
        return 'Payment Failed';
      case 'promotion':
        return 'Promotion';
      case 'system':
        return 'System';
      default:
        return type.toUpperCase();
    }
  }

  String get iconName {
    switch (type) {
      case 'new_bid':
        return 'gavel';
      case 'bid_accepted':
        return 'check_circle';
      case 'bid_rejected':
        return 'cancel';
      case 'new_order':
        return 'shopping_cart';
      case 'order_update':
      case 'order_confirmed':
      case 'order_shipped':
      case 'order_delivered':
        return 'local_shipping';
      case 'order_cancelled':
        return 'cancel';
      case 'requirement_update':
        return 'assignment';
      case 'payment_success':
        return 'payment';
      case 'payment_failed':
        return 'error';
      case 'promotion':
        return 'local_offer';
      case 'system':
        return 'info';
      default:
        return 'notifications';
    }
  }

  bool get isOrderRelated {
    return [
      'new_order',
      'order_update',
      'order_confirmed',
      'order_shipped',
      'order_delivered',
      'order_cancelled'
    ].contains(type);
  }

  bool get isBidRelated {
    return [
      'new_bid',
      'bid_accepted',
      'bid_rejected'
    ].contains(type);
  }

  bool get isPaymentRelated {
    return [
      'payment_success',
      'payment_failed'
    ].contains(type);
  }

  bool get isPromotion {
    return type == 'promotion';
  }

  bool get isSystem {
    return type == 'system';
  }

  String? get orderId {
    return data['order_id'];
  }

  String? get bidId {
    return data['bid_id'];
  }

  String? get requirementId {
    return data['requirement_id'];
  }

  String? get paymentId {
    return data['payment_id'];
  }

  bool get hasActionData {
    return orderId != null || bidId != null || requirementId != null;
  }

  String get actionText {
    if (isOrderRelated && orderId != null) {
      return 'View Order';
    } else if (isBidRelated && bidId != null) {
      return 'View Bid';
    } else if (data['requirement_id'] != null) {
      return 'View Requirement';
    } else if (isPaymentRelated && paymentId != null) {
      return 'View Payment';
    }
    return 'View Details';
  }

  Map<String, String> get navigationData {
    if (isOrderRelated && orderId != null) {
      return {'route': '/order_details', 'id': orderId!};
    } else if (isBidRelated && bidId != null) {
      return {'route': '/bid_details', 'id': bidId!};
    } else if (requirementId != null) {
      return {'route': '/requirement_details', 'id': requirementId!};
    } else if (isPaymentRelated && paymentId != null) {
      return {'route': '/payment_details', 'id': paymentId!};
    }
    return {};
  }

  bool get isRecent {
    final now = DateTime.now();
    final difference = now.difference(createdAt);
    return difference.inHours < 24;
  }

  bool get isToday {
    final now = DateTime.now();
    return createdAt.year == now.year &&
           createdAt.month == now.month &&
           createdAt.day == now.day;
  }

  bool get isYesterday {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return createdAt.year == yesterday.year &&
           createdAt.month == yesterday.month &&
           createdAt.day == yesterday.day;
  }

  String get dateGroup {
    if (isToday) {
      return 'Today';
    } else if (isYesterday) {
      return 'Yesterday';
    } else {
      final now = DateTime.now();
      final difference = now.difference(createdAt);
      if (difference.inDays < 7) {
        return '${difference.inDays} days ago';
      } else {
        return formattedDate;
      }
    }
  }
}
