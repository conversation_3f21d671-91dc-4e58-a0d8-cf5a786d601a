import 'package:flutter/material.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import '../services/analytics_service.dart';

class AnalyticsProvider extends ChangeNotifier {
  final AnalyticsService _analyticsService = AnalyticsService();
  final FirebaseAnalytics _analytics = FirebaseAnalytics.instance;
  final FirebaseCrashlytics _crashlytics = FirebaseCrashlytics.instance;

  // User engagement metrics
  Map<String, dynamic> _userMetrics = {};
  Map<String, dynamic> _appMetrics = {};
  Map<String, dynamic> _businessMetrics = {};

  Map<String, dynamic> get userMetrics => _userMetrics;
  Map<String, dynamic> get appMetrics => _appMetrics;
  Map<String, dynamic> get businessMetrics => _businessMetrics;

  // Track user events for 10M user scale
  Future<void> trackUserEvent(String eventName, Map<String, dynamic> parameters) async {
    try {
      await _analytics.logEvent(
        name: eventName,
        parameters: parameters,
      );
      
      // Custom analytics for business intelligence
      await _analyticsService.trackCustomEvent(eventName, parameters);
      
      _updateUserMetrics(eventName, parameters);
      notifyListeners();
    } catch (e) {
      _crashlytics.recordError(e, null, fatal: false);
    }
  }

  // Track screen views
  Future<void> trackScreenView(String screenName, String screenClass) async {
    try {
      await _analytics.logScreenView(
        screenName: screenName,
        screenClass: screenClass,
      );
      
      _updateAppMetrics('screen_view', {
        'screen_name': screenName,
        'screen_class': screenClass,
      });
      notifyListeners();
    } catch (e) {
      _crashlytics.recordError(e, null, fatal: false);
    }
  }

  // Track business events (orders, bids, etc.)
  Future<void> trackBusinessEvent(String eventType, Map<String, dynamic> data) async {
    try {
      await _analytics.logEvent(
        name: 'business_$eventType',
        parameters: data,
      );
      
      _updateBusinessMetrics(eventType, data);
      notifyListeners();
    } catch (e) {
      _crashlytics.recordError(e, null, fatal: false);
    }
  }

  // Track user properties for segmentation
  Future<void> setUserProperties(Map<String, String> properties) async {
    try {
      for (String key in properties.keys) {
        await _analytics.setUserProperty(
          name: key,
          value: properties[key],
        );
      }
      
      // Set user ID for Crashlytics
      if (properties.containsKey('user_id')) {
        await _crashlytics.setUserIdentifier(properties['user_id']!);
      }
    } catch (e) {
      _crashlytics.recordError(e, null, fatal: false);
    }
  }

  // Track conversion events
  Future<void> trackConversion(String conversionType, double value, String currency) async {
    try {
      await _analytics.logEvent(
        name: 'conversion',
        parameters: {
          'conversion_type': conversionType,
          'value': value,
          'currency': currency,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );
    } catch (e) {
      _crashlytics.recordError(e, null, fatal: false);
    }
  }

  // Track user retention
  Future<void> trackUserRetention(int daysSinceInstall, int sessionCount) async {
    try {
      await _analytics.logEvent(
        name: 'user_retention',
        parameters: {
          'days_since_install': daysSinceInstall,
          'session_count': sessionCount,
          'retention_cohort': _getRetentionCohort(daysSinceInstall),
        },
      );
    } catch (e) {
      _crashlytics.recordError(e, null, fatal: false);
    }
  }

  // Private helper methods
  void _updateUserMetrics(String eventName, Map<String, dynamic> parameters) {
    _userMetrics[eventName] = (_userMetrics[eventName] ?? 0) + 1;
    _userMetrics['last_event'] = eventName;
    _userMetrics['last_event_time'] = DateTime.now().toIso8601String();
  }

  void _updateAppMetrics(String metricType, Map<String, dynamic> data) {
    _appMetrics[metricType] = (_appMetrics[metricType] ?? 0) + 1;
    _appMetrics['last_${metricType}_time'] = DateTime.now().toIso8601String();
  }

  void _updateBusinessMetrics(String eventType, Map<String, dynamic> data) {
    _businessMetrics[eventType] = (_businessMetrics[eventType] ?? 0) + 1;
    _businessMetrics['last_${eventType}_time'] = DateTime.now().toIso8601String();
    
    // Track revenue if present
    if (data.containsKey('amount')) {
      _businessMetrics['total_revenue'] = (_businessMetrics['total_revenue'] ?? 0.0) + (data['amount'] ?? 0.0);
    }
  }

  String _getRetentionCohort(int daysSinceInstall) {
    if (daysSinceInstall <= 1) return 'day_1';
    if (daysSinceInstall <= 7) return 'week_1';
    if (daysSinceInstall <= 30) return 'month_1';
    if (daysSinceInstall <= 90) return 'quarter_1';
    return 'long_term';
  }

  // Get analytics summary for dashboard
  Map<String, dynamic> getAnalyticsSummary() {
    return {
      'user_metrics': _userMetrics,
      'app_metrics': _appMetrics,
      'business_metrics': _businessMetrics,
      'last_updated': DateTime.now().toIso8601String(),
    };
  }

  // Reset metrics (for testing or new sessions)
  void resetMetrics() {
    _userMetrics.clear();
    _appMetrics.clear();
    _businessMetrics.clear();
    notifyListeners();
  }
}
