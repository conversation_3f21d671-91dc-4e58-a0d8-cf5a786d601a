const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
require('dotenv').config();

// Import models
const User = require('../models/User');
const Vendor = require('../models/Vendor');
const Material = require('../models/Material');
const Service = require('../models/Service');
const Order = require('../models/Order');
const Review = require('../models/Review');

// Connect to database
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Sample data
const sampleUsers = [
  {
    name: '<PERSON><PERSON>',
    phone: '+919876543210',
    email: 'raj<PERSON>@buildbid.com',
    role: 'vendor',
    location: {
      type: 'Point',
      coordinates: [77.0266, 28.4595], // Gurgaon
      address: 'Sector 15, Gurgaon',
      city: 'Gurgaon',
      state: 'Haryana',
      pincode: '122001'
    },
    isVerified: true,
    isActive: true
  },
  {
    name: 'Priya Sharma',
    phone: '+919876543211',
    email: '<EMAIL>',
    role: 'vendor',
    location: {
      type: 'Point',
      coordinates: [77.0178, 28.4089], // Cyber City
      address: 'Cyber City, Gurgaon',
      city: 'Gurgaon',
      state: 'Haryana',
      pincode: '122002'
    },
    isVerified: true,
    isActive: true
  },
  {
    name: 'Amit Singh',
    phone: '+919876543212',
    email: '<EMAIL>',
    role: 'mistri',
    location: {
      type: 'Point',
      coordinates: [77.0266, 28.4744], // MG Road
      address: 'MG Road, Gurgaon',
      city: 'Gurgaon',
      state: 'Haryana',
      pincode: '122001'
    },
    isVerified: true,
    isActive: true
  },
  {
    name: 'Sunita Devi',
    phone: '+919876543213',
    email: '<EMAIL>',
    role: 'mistri',
    location: {
      type: 'Point',
      coordinates: [77.0400, 28.4200], // DLF Phase 2
      address: 'DLF Phase 2, Gurgaon',
      city: 'Gurgaon',
      state: 'Haryana',
      pincode: '122002'
    },
    isVerified: true,
    isActive: true
  },
  {
    name: 'Ravi Builder',
    phone: '+919876543214',
    email: '<EMAIL>',
    role: 'builder',
    location: {
      type: 'Point',
      coordinates: [77.0500, 28.4500], // Sector 18
      address: 'Sector 18, Gurgaon',
      city: 'Gurgaon',
      state: 'Haryana',
      pincode: '122015'
    },
    isVerified: true,
    isActive: true
  }
];

const sampleVendors = [
  {
    businessName: 'Kumar Construction Materials',
    businessType: 'individual',
    specializations: ['bricks', 'cement', 'sand'],
    description: 'High quality construction materials supplier with 10+ years experience',
    deliveryRadius: 15,
    minimumOrderValue: 500,
    deliveryCharges: 50,
    freeDeliveryThreshold: 2000,
    rating: { average: 4.5, count: 127 },
    isActive: true,
    isVerified: true,
    isApproved: true
  },
  {
    businessName: 'Sharma Steel & Cement',
    businessType: 'company',
    specializations: ['steel', 'cement', 'tiles'],
    description: 'Premium steel and cement supplier for all construction needs',
    deliveryRadius: 20,
    minimumOrderValue: 1000,
    deliveryCharges: 75,
    freeDeliveryThreshold: 3000,
    rating: { average: 4.8, count: 89 },
    isActive: true,
    isVerified: true,
    isApproved: true
  }
];

const sampleMaterials = [
  {
    name: 'Red Clay Bricks',
    description: 'High quality red clay bricks for construction',
    category: 'bricks',
    brand: 'Kumar Bricks',
    pricing: {
      basePrice: 8,
      unit: 'piece',
      minimumQuantity: 100
    },
    inventory: {
      currentStock: 5000,
      lowStockThreshold: 500
    },
    isActive: true,
    isApproved: true
  },
  {
    name: 'OPC Cement 53 Grade',
    description: 'Ordinary Portland Cement, 53 grade for strong construction',
    category: 'cement',
    brand: 'UltraTech',
    pricing: {
      basePrice: 370,
      unit: 'bag',
      minimumQuantity: 10
    },
    inventory: {
      currentStock: 200,
      lowStockThreshold: 20
    },
    isActive: true,
    isApproved: true
  },
  {
    name: 'TMT Steel Bars 12mm',
    description: 'Thermo-Mechanically Treated steel bars for reinforcement',
    category: 'steel',
    brand: 'TATA Steel',
    pricing: {
      basePrice: 65,
      unit: 'kg',
      minimumQuantity: 50
    },
    inventory: {
      currentStock: 1000,
      lowStockThreshold: 100
    },
    isActive: true,
    isApproved: true
  }
];

const sampleServices = [
  {
    name: 'Masonry Work',
    description: 'Expert masonry services including brickwork, plastering, and foundation work',
    category: 'masonry',
    skills: ['Brickwork', 'Plastering', 'Foundation'],
    experience: { years: 8 },
    pricing: {
      hourlyRate: 500,
      dailyRate: 3500,
      minimumHours: 4
    },
    availability: {
      isAvailable: true,
      workingDays: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday']
    },
    metrics: {
      rating: { overall: 4.6, count: 45 },
      totalJobs: 67,
      completedJobs: 63
    },
    isActive: true,
    isApproved: true
  },
  {
    name: 'Plumbing Services',
    description: 'Complete plumbing solutions including pipe fitting, repairs, and installations',
    category: 'plumbing',
    skills: ['Pipe Fitting', 'Leak Repairs', 'Bathroom Installation'],
    experience: { years: 6 },
    pricing: {
      hourlyRate: 600,
      dailyRate: 4200,
      minimumHours: 2
    },
    availability: {
      isAvailable: true,
      workingDays: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday']
    },
    metrics: {
      rating: { overall: 4.3, count: 32 },
      totalJobs: 45,
      completedJobs: 42
    },
    isActive: true,
    isApproved: true
  }
];

// Seed function
const seedDatabase = async () => {
  try {
    console.log('🌱 Starting database seeding...');

    // Clear existing data
    await User.deleteMany({});
    await Vendor.deleteMany({});
    await Material.deleteMany({});
    await Service.deleteMany({});
    await Order.deleteMany({});
    await Review.deleteMany({});
    console.log('🗑️  Cleared existing data');

    // Create users
    const users = await User.insertMany(sampleUsers);
    console.log(`👥 Created ${users.length} users`);

    // Create vendors
    const vendorUsers = users.filter(user => user.role === 'vendor');
    const vendors = [];
    
    for (let i = 0; i < sampleVendors.length; i++) {
      const vendor = new Vendor({
        ...sampleVendors[i],
        userId: vendorUsers[i]._id
      });
      vendors.push(await vendor.save());
    }
    console.log(`🏪 Created ${vendors.length} vendors`);

    // Create materials
    const materials = [];
    for (let i = 0; i < sampleMaterials.length; i++) {
      const material = new Material({
        ...sampleMaterials[i],
        vendorId: vendors[i % vendors.length]._id
      });
      materials.push(await material.save());
    }
    console.log(`📦 Created ${materials.length} materials`);

    // Create services
    const mistriUsers = users.filter(user => user.role === 'mistri');
    const services = [];
    
    for (let i = 0; i < sampleServices.length; i++) {
      const service = new Service({
        ...sampleServices[i],
        providerId: mistriUsers[i % mistriUsers.length]._id
      });
      services.push(await service.save());
    }
    console.log(`🔧 Created ${services.length} services`);

    // Create sample orders
    const builderUsers = users.filter(user => user.role === 'builder');
    if (builderUsers.length > 0 && vendors.length > 0 && materials.length > 0) {
      const sampleOrder = new Order({
        customerId: builderUsers[0]._id,
        vendorId: vendors[0]._id,
        items: [{
          materialId: materials[0]._id,
          name: materials[0].name,
          quantity: 100,
          unit: materials[0].pricing.unit,
          pricePerUnit: materials[0].pricing.basePrice,
          totalPrice: materials[0].pricing.basePrice * 100
        }],
        pricing: {
          subtotal: materials[0].pricing.basePrice * 100,
          tax: { gst: (materials[0].pricing.basePrice * 100 * 18) / 100, total: (materials[0].pricing.basePrice * 100 * 18) / 100 },
          delivery: { charges: 50, freeDelivery: false },
          total: (materials[0].pricing.basePrice * 100) + ((materials[0].pricing.basePrice * 100 * 18) / 100) + 50
        },
        delivery: {
          address: {
            street: 'Sector 18, Gurgaon',
            city: 'Gurgaon',
            state: 'Haryana',
            pincode: '122015'
          },
          timeSlot: {
            date: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
            slot: 'morning'
          }
        },
        payment: {
          method: 'cod',
          status: 'pending'
        },
        status: 'confirmed'
      });
      
      await sampleOrder.save();
      console.log('📋 Created 1 sample order');
    }

    // Create sample reviews
    if (vendors.length > 0 && builderUsers.length > 0) {
      const sampleReview = new Review({
        reviewType: 'vendor',
        reviewerId: builderUsers[0]._id,
        targetId: vendors[0]._id,
        targetModel: 'Vendor',
        rating: {
          overall: 5,
          quality: 5,
          delivery: 4,
          service: 5
        },
        title: 'Excellent service!',
        comment: 'Great quality materials and fast delivery. Highly recommended!',
        isVerified: true,
        verifiedPurchase: true
      });
      
      await sampleReview.save();
      console.log('⭐ Created 1 sample review');
    }

    console.log('✅ Database seeding completed successfully!');
    console.log('\n📊 Summary:');
    console.log(`👥 Users: ${users.length}`);
    console.log(`🏪 Vendors: ${vendors.length}`);
    console.log(`📦 Materials: ${materials.length}`);
    console.log(`🔧 Services: ${services.length}`);
    console.log(`📋 Orders: 1`);
    console.log(`⭐ Reviews: 1`);

  } catch (error) {
    console.error('❌ Database seeding failed:', error);
  } finally {
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
  }
};

// Run seeding if called directly
if (require.main === module) {
  connectDB().then(() => {
    seedDatabase();
  });
}

module.exports = { seedDatabase, connectDB };
