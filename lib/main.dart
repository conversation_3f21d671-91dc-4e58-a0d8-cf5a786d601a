import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'providers/auth_provider.dart';
import 'providers/theme_provider.dart';
import 'providers/language_provider.dart';
import 'providers/requirement_provider.dart';
import 'providers/inventory_provider.dart';
import 'providers/order_provider.dart';
import 'providers/notification_provider.dart';
import 'providers/product_provider.dart';
import 'providers/service_provider.dart';
// import 'services/firebase_service.dart';
import 'screens/common/splash_screen.dart';
import 'screens/common/role_selection_screen.dart';
import 'screens/builder/home_screen.dart' as builder;
import 'screens/mistri/home_screen.dart' as mistri;
import 'screens/vendor/home_screen.dart' as vendor;
import 'routes/app_routes.dart';
import 'utils/app_colors.dart';
import 'utils/constants.dart';
import 'utils/app_theme.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase (commented out for demo)
  // await Firebase.initializeApp();
  // await FirebaseService.initialize();

  // Set system UI overlay style
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      systemNavigationBarColor: Colors.white,
      systemNavigationBarIconBrightness: Brightness.dark,
    ),
  );

  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // Initialize SharedPreferences
  final prefs = await SharedPreferences.getInstance();

  runApp(BuildBidApp(prefs: prefs));
}

class BuildBidApp extends StatelessWidget {
  final SharedPreferences prefs;

  const BuildBidApp({Key? key, required this.prefs}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider()..initializeAuth()),
        ChangeNotifierProvider(create: (_) => ThemeProvider(prefs)),
        ChangeNotifierProvider(create: (_) => LanguageProvider(prefs)),
        ChangeNotifierProvider(create: (_) => RequirementProvider()),
        ChangeNotifierProvider(create: (_) => InventoryProvider()),
        ChangeNotifierProvider(create: (_) => OrderProvider()),
        ChangeNotifierProvider(create: (_) => NotificationProvider()),
        ChangeNotifierProvider(create: (_) => ProductProvider()),
        ChangeNotifierProvider(create: (_) => ServiceProvider()),
      ],
      child: Consumer3<ThemeProvider, LanguageProvider, AuthProvider>(
        builder: (context, themeProvider, languageProvider, authProvider, child) {
          return MaterialApp(
            title: 'BuildBid - Smart Construction Starts Here',
            debugShowCheckedModeBanner: false,
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: themeProvider.themeMode,
            locale: languageProvider.locale,
            supportedLocales: const [
              Locale('en', 'US'),
              Locale('hi', 'IN'),
            ],
            onGenerateRoute: AppRoutes.generateRoute,
            home: _getInitialScreen(authProvider),
          );
        },
      ),
    );
  }

  Widget _getInitialScreen(AuthProvider authProvider) {
    if (!authProvider.isInitialized) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (authProvider.isAuthenticated && authProvider.currentUser != null) {
      final userRole = authProvider.currentUser!.role;
      switch (userRole) {
        case 'builder':
          return const builder.HomeScreen();
        case 'mistri':
          return const mistri.HomeScreen();
        case 'vendor':
          return const vendor.HomeScreen();
        default:
          return const RoleSelectionScreen();
      }
    }

    return const SplashScreen();
  }
}
