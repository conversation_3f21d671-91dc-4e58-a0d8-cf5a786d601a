import 'package:flutter/material.dart';
import '../models/product_model.dart';
import '../models/cart_model.dart';
import '../services/sample_data_service.dart';

class ProductProvider extends ChangeNotifier {
  List<ProductModel> _products = [];
  List<CartItemModel> _cart = [];
  bool _isLoading = false;
  String? _errorMessage;
  String _selectedCategory = '';
  String _searchQuery = '';

  // Getters
  List<ProductModel> get products => _products;
  List<CartItemModel> get cart => _cart;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  String get selectedCategory => _selectedCategory;
  String get searchQuery => _searchQuery;

  // Cart calculations
  double get cartTotal => _cart.fold(0.0, (sum, item) => sum + item.totalPrice);
  int get cartItemCount => _cart.fold(0, (sum, item) => sum + item.quantity);
  
  // Get unique vendors in cart
  List<String> get cartVendors => _cart.map((item) => item.vendorId).toSet().toList();

  // Get cart items by vendor
  Map<String, List<CartItemModel>> get cartByVendor {
    Map<String, List<CartItemModel>> vendorCart = {};
    for (var item in _cart) {
      if (!vendorCart.containsKey(item.vendorId)) {
        vendorCart[item.vendorId] = [];
      }
      vendorCart[item.vendorId]!.add(item);
    }
    return vendorCart;
  }

  // Load products
  Future<void> loadProducts({String? category}) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      await Future.delayed(const Duration(milliseconds: 500)); // Simulate network delay
      
      _products = SampleDataService.getSampleProducts();
      
      if (category != null && category.isNotEmpty) {
        _products = _products.where((p) => p.category.toLowerCase() == category.toLowerCase()).toList();
      }

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _errorMessage = e.toString();
      _isLoading = false;
      notifyListeners();
    }
  }

  // Search products
  void searchProducts(String query) {
    _searchQuery = query;
    if (query.isEmpty) {
      loadProducts(category: _selectedCategory.isEmpty ? null : _selectedCategory);
    } else {
      _products = _products.where((product) =>
          product.name.toLowerCase().contains(query.toLowerCase()) ||
          product.description.toLowerCase().contains(query.toLowerCase()) ||
          product.category.toLowerCase().contains(query.toLowerCase())
      ).toList();
    }
    notifyListeners();
  }

  // Filter by category
  void filterByCategory(String category) {
    _selectedCategory = category;
    loadProducts(category: category);
  }

  // Clear filters
  void clearFilters() {
    _selectedCategory = '';
    _searchQuery = '';
    loadProducts();
  }

  // Add to cart
  void addToCart(ProductModel product, int quantity) {
    final existingIndex = _cart.indexWhere((item) => item.inventoryId == product.id);

    if (existingIndex >= 0) {
      // Update existing item
      _cart[existingIndex] = _cart[existingIndex].copyWith(
        quantity: _cart[existingIndex].quantity + quantity,
      );
    } else {
      // Add new item
      _cart.add(CartItemModel(
        inventoryId: product.id,
        vendorId: product.vendorId,
        vendorName: product.vendorName,
        materialType: product.category,
        description: product.name,
        price: product.price,
        quantity: quantity,
        unit: product.unit,
        imageUrl: product.imageUrls.isNotEmpty ? product.imageUrls.first : null,
        specifications: product.specifications,
      ));
    }
    notifyListeners();
  }

  // Remove from cart
  void removeFromCart(String productId) {
    _cart.removeWhere((item) => item.inventoryId == productId);
    notifyListeners();
  }

  // Update cart item quantity
  void updateCartItemQuantity(String productId, int quantity) {
    if (quantity <= 0) {
      removeFromCart(productId);
      return;
    }

    final index = _cart.indexWhere((item) => item.inventoryId == productId);
    if (index >= 0) {
      _cart[index] = _cart[index].copyWith(quantity: quantity);
      notifyListeners();
    }
  }

  // Clear cart
  void clearCart() {
    _cart.clear();
    notifyListeners();
  }

  // Get cart item quantity for a product
  int getCartItemQuantity(String productId) {
    try {
      final item = _cart.firstWhere((item) => item.inventoryId == productId);
      return item.quantity;
    } catch (e) {
      return 0;
    }
  }

  // Check if product is in cart
  bool isInCart(String productId) {
    return _cart.any((item) => item.inventoryId == productId);
  }

  // Get product by ID
  ProductModel? getProductById(String productId) {
    try {
      return _products.firstWhere((product) => product.id == productId);
    } catch (e) {
      return null;
    }
  }

  // Get products by category
  List<ProductModel> getProductsByCategory(String category) {
    return _products.where((product) => 
        product.category.toLowerCase() == category.toLowerCase()).toList();
  }

  // Get featured products
  List<ProductModel> getFeaturedProducts() {
    return _products.where((product) => product.rating >= 4.0).take(10).toList();
  }

  // Get products by vendor
  List<ProductModel> getProductsByVendor(String vendorId) {
    return _products.where((product) => product.vendorId == vendorId).toList();
  }

  // Calculate delivery charges (mock implementation)
  double calculateDeliveryCharges(String vendorId, double orderValue) {
    if (orderValue >= 1000) return 0.0; // Free delivery above ₹1000
    return 50.0; // ₹50 delivery charge
  }

  // Calculate tax (mock implementation)
  double calculateTax(double amount) {
    return amount * 0.18; // 18% GST
  }

  // Get order summary
  Map<String, dynamic> getOrderSummary() {
    final subtotal = cartTotal;
    final tax = calculateTax(subtotal);
    final deliveryCharges = cartVendors.fold(0.0, (sum, vendorId) {
      final vendorItems = _cart.where((item) => item.vendorId == vendorId).toList();
      final vendorTotal = vendorItems.fold(0.0, (sum, item) => sum + item.totalPrice);
      return sum + calculateDeliveryCharges(vendorId, vendorTotal);
    });
    final total = subtotal + tax + deliveryCharges;

    return {
      'subtotal': subtotal,
      'tax': tax,
      'deliveryCharges': deliveryCharges,
      'total': total,
      'itemCount': cartItemCount,
      'vendorCount': cartVendors.length,
    };
  }

  // Apply coupon (mock implementation)
  double applyCoupon(String couponCode, double amount) {
    switch (couponCode.toUpperCase()) {
      case 'FIRST10':
        return amount * 0.10; // 10% discount
      case 'SAVE50':
        return 50.0; // ₹50 off
      case 'BULK20':
        return amount >= 2000 ? amount * 0.20 : 0.0; // 20% off on orders above ₹2000
      default:
        return 0.0;
    }
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
  }
}
