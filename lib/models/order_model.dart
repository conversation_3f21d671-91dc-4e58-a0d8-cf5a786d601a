// import 'package:cloud_firestore/cloud_firestore.dart';
import 'cart_model.dart';

class OrderModel {
  final String id;
  final String userId;
  final String vendorId;
  final String vendorName;
  final List<CartItemModel> items;
  final double totalAmount;
  final String deliveryAddress;
  final Map<String, dynamic> location;
  final String status; // placed, confirmed, processing, shipped, delivered, cancelled
  final String paymentStatus; // pending, paid, failed, refunded
  final DateTime createdAt;
  final DateTime? updatedAt;
  final DateTime? deliveryEta;
  final String? notes;
  final String? cancellationReason;
  final String? trackingId;

  OrderModel({
    required this.id,
    required this.userId,
    required this.vendorId,
    required this.vendorName,
    required this.items,
    required this.totalAmount,
    required this.deliveryAddress,
    required this.location,
    required this.status,
    this.paymentStatus = 'pending',
    required this.createdAt,
    this.updatedAt,
    this.deliveryEta,
    this.notes,
    this.cancellationReason,
    this.trackingId,
  });

  // factory OrderModel.fromFirestore(DocumentSnapshot doc) {
  //   final data = doc.data() as Map<String, dynamic>;
  //   return OrderModel(
  //     id: doc.id,
  //     userId: data['user_id'] ?? '',
  //     vendorId: data['vendor_id'] ?? '',
  //     vendorName: data['vendor_name'] ?? '',
  //     items: (data['items'] as List<dynamic>?)
  //         ?.map((item) => CartItemModel.fromMap(item as Map<String, dynamic>))
  //         .toList() ?? [],
  //     totalAmount: (data['total_amount'] ?? 0.0).toDouble(),
  //     deliveryAddress: data['delivery_address'] ?? '',
  //     location: data['location'] ?? {},
  //     status: data['status'] ?? 'placed',
  //     paymentStatus: data['payment_status'] ?? 'pending',
  //     createdAt: (data['created_at'] as Timestamp?)?.toDate() ?? DateTime.now(),
  //     updatedAt: (data['updated_at'] as Timestamp?)?.toDate(),
  //     deliveryEta: (data['delivery_eta'] as Timestamp?)?.toDate(),
  //     notes: data['notes'],
  //     cancellationReason: data['cancellation_reason'],
  //     trackingId: data['tracking_id'],
  //   );
  // }

  factory OrderModel.fromMap(Map<String, dynamic> data, String id) {
    return OrderModel(
      id: id,
      userId: data['user_id'] ?? '',
      vendorId: data['vendor_id'] ?? '',
      vendorName: data['vendor_name'] ?? '',
      items: (data['items'] as List<dynamic>?)
          ?.map((item) => CartItemModel.fromMap(item as Map<String, dynamic>))
          .toList() ?? [],
      totalAmount: (data['total_amount'] ?? 0.0).toDouble(),
      deliveryAddress: data['delivery_address'] ?? '',
      location: data['location'] ?? {},
      status: data['status'] ?? 'placed',
      paymentStatus: data['payment_status'] ?? 'pending',
      createdAt: data['created_at'] is String
          ? DateTime.parse(data['created_at'])
          : DateTime.now(),
      updatedAt: data['updated_at'] is String
          ? DateTime.parse(data['updated_at'])
          : null,
      deliveryEta: data['delivery_eta'] is String
          ? DateTime.parse(data['delivery_eta'])
          : null,
      notes: data['notes'],
      cancellationReason: data['cancellation_reason'],
      trackingId: data['tracking_id'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'user_id': userId,
      'vendor_id': vendorId,
      'vendor_name': vendorName,
      'items': items.map((item) => item.toMap()).toList(),
      'total_amount': totalAmount,
      'delivery_address': deliveryAddress,
      'location': location,
      'status': status,
      'payment_status': paymentStatus,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'delivery_eta': deliveryEta?.toIso8601String(),
      'notes': notes,
      'cancellation_reason': cancellationReason,
      'tracking_id': trackingId,
    };
  }

  OrderModel copyWith({
    String? userId,
    String? vendorId,
    String? vendorName,
    List<CartItemModel>? items,
    double? totalAmount,
    String? deliveryAddress,
    Map<String, dynamic>? location,
    String? status,
    String? paymentStatus,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? deliveryEta,
    String? notes,
    String? cancellationReason,
    String? trackingId,
  }) {
    return OrderModel(
      id: id,
      userId: userId ?? this.userId,
      vendorId: vendorId ?? this.vendorId,
      vendorName: vendorName ?? this.vendorName,
      items: items ?? this.items,
      totalAmount: totalAmount ?? this.totalAmount,
      deliveryAddress: deliveryAddress ?? this.deliveryAddress,
      location: location ?? this.location,
      status: status ?? this.status,
      paymentStatus: paymentStatus ?? this.paymentStatus,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deliveryEta: deliveryEta ?? this.deliveryEta,
      notes: notes ?? this.notes,
      cancellationReason: cancellationReason ?? this.cancellationReason,
      trackingId: trackingId ?? this.trackingId,
    );
  }

  String get statusDisplay {
    switch (status) {
      case 'placed':
        return 'Order Placed';
      case 'confirmed':
        return 'Confirmed';
      case 'processing':
        return 'Processing';
      case 'shipped':
        return 'Shipped';
      case 'delivered':
        return 'Delivered';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status.toUpperCase();
    }
  }

  String get paymentStatusDisplay {
    switch (paymentStatus) {
      case 'pending':
        return 'Payment Pending';
      case 'paid':
        return 'Paid';
      case 'failed':
        return 'Payment Failed';
      case 'refunded':
        return 'Refunded';
      default:
        return paymentStatus.toUpperCase();
    }
  }

  String get totalAmountDisplay {
    return '₹${totalAmount.toStringAsFixed(0)}';
  }

  int get totalItems {
    return items.fold(0, (sum, item) => sum + item.quantity);
  }

  String get itemsDisplay {
    if (items.length == 1) {
      return '${items.first.materialType} (${items.first.quantity} ${items.first.unit})';
    } else {
      return '${items.length} items (${totalItems} total)';
    }
  }

  bool get canBeCancelled {
    return ['placed', 'confirmed'].contains(status) && paymentStatus != 'paid';
  }

  bool get isActive {
    return !['delivered', 'cancelled'].contains(status);
  }

  bool get isCompleted {
    return status == 'delivered';
  }

  bool get isCancelled {
    return status == 'cancelled';
  }

  bool get isPaid {
    return paymentStatus == 'paid';
  }

  String get deliveryEtaDisplay {
    if (deliveryEta == null) return 'ETA not available';
    
    final now = DateTime.now();
    final difference = deliveryEta!.difference(now);
    
    if (difference.isNegative) {
      return 'Overdue';
    } else if (difference.inDays > 0) {
      return '${difference.inDays} days';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hours';
    } else {
      return '${difference.inMinutes} minutes';
    }
  }

  String get orderDate {
    return '${createdAt.day}/${createdAt.month}/${createdAt.year}';
  }

  String get orderTime {
    return '${createdAt.hour.toString().padLeft(2, '0')}:${createdAt.minute.toString().padLeft(2, '0')}';
  }

  List<String> get statusSteps {
    return ['placed', 'confirmed', 'processing', 'shipped', 'delivered'];
  }

  int get currentStepIndex {
    return statusSteps.indexOf(status);
  }

  double get progressPercentage {
    if (isCancelled) return 0.0;
    final index = currentStepIndex;
    if (index == -1) return 0.0;
    return (index + 1) / statusSteps.length;
  }

  String get nextStatus {
    final currentIndex = statusSteps.indexOf(status);
    if (currentIndex == -1 || currentIndex == statusSteps.length - 1) {
      return status;
    }
    return statusSteps[currentIndex + 1];
  }

  bool get hasTrackingId {
    return trackingId != null && trackingId!.isNotEmpty;
  }

  String get locationDisplay {
    final city = location['city'] ?? '';
    final state = location['state'] ?? '';
    final pincode = location['pincode'] ?? '';
    
    if (city.isNotEmpty && state.isNotEmpty) {
      return pincode.isNotEmpty ? '$city, $state - $pincode' : '$city, $state';
    }
    return 'Location not specified';
  }
}
