import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:firebase_analytics/firebase_analytics.dart';

class AnalyticsService {
  static final AnalyticsService _instance = AnalyticsService._internal();
  factory AnalyticsService() => _instance;
  AnalyticsService._internal();

  final FirebaseAnalytics _analytics = FirebaseAnalytics.instance;
  final String _baseUrl = 'https://api.buildbid.com/v1/analytics';
  
  // Custom analytics for business intelligence
  Future<void> trackCustomEvent(String eventName, Map<String, dynamic> parameters) async {
    try {
      // Send to custom analytics backend for detailed business analysis
      final response = await http.post(
        Uri.parse('$_baseUrl/events'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${await _getAuthToken()}',
        },
        body: json.encode({
          'event_name': eventName,
          'parameters': parameters,
          'timestamp': DateTime.now().toIso8601String(),
          'user_id': await _getUserId(),
          'session_id': await _getSessionId(),
          'app_version': '1.0.0',
          'platform': 'flutter',
        }),
      );

      if (response.statusCode != 200) {
        print('Analytics event failed: ${response.body}');
      }
    } catch (e) {
      print('Analytics error: $e');
    }
  }

  // Track user journey for conversion optimization
  Future<void> trackUserJourney(String step, Map<String, dynamic> context) async {
    try {
      await _analytics.logEvent(
        name: 'user_journey',
        parameters: {
          'journey_step': step,
          'context': json.encode(context),
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );

      // Send to custom backend for funnel analysis
      await trackCustomEvent('user_journey', {
        'step': step,
        'context': context,
      });
    } catch (e) {
      print('User journey tracking error: $e');
    }
  }

  // Track business KPIs
  Future<void> trackBusinessKPI(String kpiName, double value, Map<String, dynamic> metadata) async {
    try {
      await _analytics.logEvent(
        name: 'business_kpi',
        parameters: {
          'kpi_name': kpiName,
          'kpi_value': value,
          'metadata': json.encode(metadata),
        },
      );

      // Send to business intelligence backend
      await http.post(
        Uri.parse('$_baseUrl/kpis'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${await _getAuthToken()}',
        },
        body: json.encode({
          'kpi_name': kpiName,
          'value': value,
          'metadata': metadata,
          'timestamp': DateTime.now().toIso8601String(),
          'user_id': await _getUserId(),
        }),
      );
    } catch (e) {
      print('Business KPI tracking error: $e');
    }
  }

  // Track user engagement metrics
  Future<void> trackEngagement(String engagementType, Duration duration, Map<String, dynamic> details) async {
    try {
      await _analytics.logEvent(
        name: 'user_engagement',
        parameters: {
          'engagement_type': engagementType,
          'duration_seconds': duration.inSeconds,
          'details': json.encode(details),
        },
      );
    } catch (e) {
      print('Engagement tracking error: $e');
    }
  }

  // Track conversion funnel
  Future<void> trackConversionFunnel(String funnelStep, String funnelName, Map<String, dynamic> data) async {
    try {
      await _analytics.logEvent(
        name: 'conversion_funnel',
        parameters: {
          'funnel_name': funnelName,
          'funnel_step': funnelStep,
          'step_data': json.encode(data),
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );

      // Send to conversion optimization backend
      await trackCustomEvent('conversion_funnel', {
        'funnel_name': funnelName,
        'step': funnelStep,
        'data': data,
      });
    } catch (e) {
      print('Conversion funnel tracking error: $e');
    }
  }

  // Track A/B test events
  Future<void> trackABTest(String testName, String variant, String event, Map<String, dynamic> data) async {
    try {
      await _analytics.logEvent(
        name: 'ab_test_event',
        parameters: {
          'test_name': testName,
          'variant': variant,
          'event': event,
          'data': json.encode(data),
        },
      );
    } catch (e) {
      print('A/B test tracking error: $e');
    }
  }

  // Track error events for debugging
  Future<void> trackError(String errorType, String errorMessage, Map<String, dynamic> context) async {
    try {
      await _analytics.logEvent(
        name: 'app_error',
        parameters: {
          'error_type': errorType,
          'error_message': errorMessage,
          'context': json.encode(context),
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );
    } catch (e) {
      print('Error tracking error: $e');
    }
  }

  // Track feature usage
  Future<void> trackFeatureUsage(String featureName, Map<String, dynamic> usage) async {
    try {
      await _analytics.logEvent(
        name: 'feature_usage',
        parameters: {
          'feature_name': featureName,
          'usage_data': json.encode(usage),
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );
    } catch (e) {
      print('Feature usage tracking error: $e');
    }
  }

  // Helper methods
  Future<String> _getAuthToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('auth_token') ?? '';
  }

  Future<String> _getUserId() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('user_id') ?? 'anonymous';
  }

  Future<String> _getSessionId() async {
    final prefs = await SharedPreferences.getInstance();
    String? sessionId = prefs.getString('session_id');
    
    if (sessionId == null) {
      sessionId = DateTime.now().millisecondsSinceEpoch.toString();
      await prefs.setString('session_id', sessionId);
    }
    
    return sessionId;
  }

  // Batch analytics for performance
  final List<Map<String, dynamic>> _eventQueue = [];
  
  Future<void> queueEvent(String eventName, Map<String, dynamic> parameters) async {
    _eventQueue.add({
      'event_name': eventName,
      'parameters': parameters,
      'timestamp': DateTime.now().toIso8601String(),
    });

    // Send batch when queue reaches threshold
    if (_eventQueue.length >= 10) {
      await _sendBatchEvents();
    }
  }

  Future<void> _sendBatchEvents() async {
    if (_eventQueue.isEmpty) return;

    try {
      final events = List.from(_eventQueue);
      _eventQueue.clear();

      await http.post(
        Uri.parse('$_baseUrl/events/batch'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${await _getAuthToken()}',
        },
        body: json.encode({
          'events': events,
          'user_id': await _getUserId(),
          'session_id': await _getSessionId(),
        }),
      );
    } catch (e) {
      print('Batch analytics error: $e');
    }
  }

  // Force send remaining events
  Future<void> flush() async {
    await _sendBatchEvents();
  }
}
