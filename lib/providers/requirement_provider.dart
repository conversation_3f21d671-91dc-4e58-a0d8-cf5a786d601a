import 'package:flutter/material.dart';
import '../models/requirement_model.dart';
import '../models/bid_model.dart';
import '../services/sample_data_service.dart';

class RequirementProvider extends ChangeNotifier {
  // final FirestoreService _firestoreService = FirestoreService();

  List<RequirementModel> _requirements = [];
  List<RequirementModel> _activeRequirements = [];
  List<BidModel> _bids = [];
  List<BidModel> _requirementBids = [];

  bool _isLoading = false;
  String? _errorMessage;
  RequirementModel? _selectedRequirement;

  // Getters
  List<RequirementModel> get requirements => _requirements;
  List<RequirementModel> get activeRequirements => _activeRequirements;
  List<BidModel> get bids => _bids;
  List<BidModel> get requirementBids => _requirementBids;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  RequirementModel? get selectedRequirement => _selectedRequirement;

  // REQUIREMENT OPERATIONS

  // Create requirement
  Future<bool> createRequirement(RequirementModel requirement) async {
    _setLoading(true);
    _clearError();

    try {
      await Future.delayed(const Duration(milliseconds: 500)); // Simulate network delay

      String requirementId = 'req_${DateTime.now().millisecondsSinceEpoch}';
      RequirementModel updatedRequirement = requirement.copyWith(id: requirementId);
      _requirements.insert(0, updatedRequirement);

      _setLoading(false);
      return true;
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
      return false;
    }
  }

  // Post requirement (alias for createRequirement)
  Future<bool> postRequirement(RequirementModel requirement) async {
    return await createRequirement(requirement);
  }

  // Load requirements by builder
  Future<void> loadRequirementsByBuilder(String builderId) async {
    _setLoading(true);
    _clearError();

    try {
      await Future.delayed(const Duration(milliseconds: 500)); // Simulate network delay
      _requirements = SampleDataService.getSampleRequirements();
      _setLoading(false);
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
    }
  }

  // Load active requirements for vendors
  void loadActiveRequirements({String? city, String? materialType}) {
    _firestoreService.getActiveRequirements(
      city: city,
      materialType: materialType,
    ).listen(
      (requirements) {
        _activeRequirements = requirements;
        notifyListeners();
      },
      onError: (error) {
        _setError(error.toString());
      },
    );
  }

  // Update requirement
  Future<bool> updateRequirement(RequirementModel requirement) async {
    _setLoading(true);
    _clearError();

    try {
      await _firestoreService.updateRequirement(requirement);

      // Update local list
      int index = _requirements.indexWhere((r) => r.id == requirement.id);
      if (index != -1) {
        _requirements[index] = requirement;
      }

      // Update selected requirement if it's the same
      if (_selectedRequirement?.id == requirement.id) {
        _selectedRequirement = requirement;
      }

      _setLoading(false);
      return true;
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
      return false;
    }
  }

  // Delete requirement
  Future<bool> deleteRequirement(String requirementId) async {
    _setLoading(true);
    _clearError();

    try {
      await _firestoreService.deleteRequirement(requirementId);

      // Remove from local list
      _requirements.removeWhere((r) => r.id == requirementId);
      _activeRequirements.removeWhere((r) => r.id == requirementId);

      // Clear selected requirement if it's the deleted one
      if (_selectedRequirement?.id == requirementId) {
        _selectedRequirement = null;
      }

      _setLoading(false);
      return true;
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
      return false;
    }
  }

  // BID OPERATIONS

  // Create bid
  Future<bool> createBid(BidModel bid) async {
    _setLoading(true);
    _clearError();

    try {
      String bidId = await _firestoreService.createBid(bid);

      // Add to local list with the generated ID
      BidModel updatedBid = bid.copyWith(id: bidId);
      _bids.insert(0, updatedBid);

      _setLoading(false);
      return true;
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
      return false;
    }
  }

  // Load bids for requirement
  void loadBidsForRequirement(String requirementId) {
    _firestoreService.getBidsForRequirement(requirementId).listen(
      (bids) {
        _requirementBids = bids;
        notifyListeners();
      },
      onError: (error) {
        _setError(error.toString());
      },
    );
  }

  // Load bids by vendor
  void loadBidsByVendor(String vendorId) {
    _firestoreService.getBidsByVendor(vendorId).listen(
      (bids) {
        _bids = bids;
        notifyListeners();
      },
      onError: (error) {
        _setError(error.toString());
      },
    );
  }

  // Update bid
  Future<bool> updateBid(BidModel bid) async {
    _setLoading(true);
    _clearError();

    try {
      await _firestoreService.updateBid(bid);

      // Update local lists
      int index = _bids.indexWhere((b) => b.id == bid.id);
      if (index != -1) {
        _bids[index] = bid;
      }

      int reqBidIndex = _requirementBids.indexWhere((b) => b.id == bid.id);
      if (reqBidIndex != -1) {
        _requirementBids[reqBidIndex] = bid;
      }

      _setLoading(false);
      return true;
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
      return false;
    }
  }

  // Accept bid
  Future<bool> acceptBid(String bidId, String requirementId) async {
    _setLoading(true);
    _clearError();

    try {
      await _firestoreService.acceptBid(bidId, requirementId);

      // Update local data will be handled by the stream listeners
      _setLoading(false);
      return true;
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
      return false;
    }
  }

  // UTILITY METHODS

  // Set selected requirement
  void setSelectedRequirement(RequirementModel? requirement) {
    _selectedRequirement = requirement;
    notifyListeners();
  }

  // Get requirement by ID
  RequirementModel? getRequirementById(String id) {
    try {
      return _requirements.firstWhere((r) => r.id == id);
    } catch (e) {
      try {
        return _activeRequirements.firstWhere((r) => r.id == id);
      } catch (e) {
        return null;
      }
    }
  }

  // Get bid by ID
  BidModel? getBidById(String id) {
    try {
      return _bids.firstWhere((b) => b.id == id);
    } catch (e) {
      try {
        return _requirementBids.firstWhere((b) => b.id == id);
      } catch (e) {
        return null;
      }
    }
  }

  // Filter requirements by material type
  List<RequirementModel> getRequirementsByMaterialType(String materialType) {
    return _activeRequirements.where((r) => r.materialType == materialType).toList();
  }

  // Filter requirements by city
  List<RequirementModel> getRequirementsByCity(String city) {
    return _activeRequirements.where((r) => r.city.toLowerCase() == city.toLowerCase()).toList();
  }

  // Get requirements with budget range
  List<RequirementModel> getRequirementsInBudgetRange(double minBudget, double maxBudget) {
    return _activeRequirements.where((r) {
      if (r.budgetMin != null && r.budgetMax != null) {
        return r.budgetMin! >= minBudget && r.budgetMax! <= maxBudget;
      }
      return false;
    }).toList();
  }

  // Get winning bids for vendor
  List<BidModel> getWinningBids() {
    return _bids.where((b) => b.status == 'won').toList();
  }

  // Get active bids for vendor
  List<BidModel> getActiveBids() {
    return _bids.where((b) => b.status == 'active').toList();
  }

  // Clear all data
  void clearData() {
    _requirements.clear();
    _activeRequirements.clear();
    _bids.clear();
    _requirementBids.clear();
    _selectedRequirement = null;
    _clearError();
    notifyListeners();
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Refresh data
  void refreshRequirements(String builderId) {
    loadRequirementsByBuilder(builderId);
  }

  void refreshActiveRequirements({String? city, String? materialType}) {
    loadActiveRequirements(city: city, materialType: materialType);
  }

  void refreshBids(String vendorId) {
    loadBidsByVendor(vendorId);
  }

  void refreshRequirementBids(String requirementId) {
    loadBidsForRequirement(requirementId);
  }

  // Get unread bid count for builder
  int getUnreadBidCount() {
    // Mock implementation - replace with actual logic
    return _requirements
        .where((req) => req.status == 'active' && req.bidCount > 0)
        .length;
  }



  // Get total bids count
  int get totalBids {
    return _requirements.fold(0, (sum, req) => sum + req.bidCount);
  }
}
