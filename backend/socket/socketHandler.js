const jwt = require('jsonwebtoken');

const socketHandler = (io) => {
  // Middleware for socket authentication
  io.use((socket, next) => {
    try {
      const token = socket.handshake.auth.token;
      if (!token) {
        return next(new Error('Authentication error'));
      }

      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      socket.userId = decoded.userId;
      socket.userRole = decoded.role;
      next();
    } catch (error) {
      next(new Error('Authentication error'));
    }
  });

  io.on('connection', (socket) => {
    console.log(`User ${socket.userId} connected`);

    // Join user to their personal room
    socket.join(`user_${socket.userId}`);

    // Join vendor to vendor room if applicable
    if (socket.userRole === 'vendor') {
      socket.join('vendors');
    }

    // Handle order tracking
    socket.on('track_order', (orderId) => {
      socket.join(`order_${orderId}`);
      console.log(`User ${socket.userId} tracking order ${orderId}`);
    });

    // Handle service tracking
    socket.on('track_service', (serviceId) => {
      socket.join(`service_${serviceId}`);
      console.log(`User ${socket.userId} tracking service ${serviceId}`);
    });

    // Handle location updates for delivery tracking
    socket.on('location_update', (data) => {
      if (socket.userRole === 'vendor') {
        socket.to(`order_${data.orderId}`).emit('delivery_location', {
          latitude: data.latitude,
          longitude: data.longitude,
          timestamp: new Date()
        });
      }
    });

    // Handle chat messages
    socket.on('send_message', (data) => {
      const { orderId, message, type } = data;
      
      // Broadcast to all users in the order room
      socket.to(`order_${orderId}`).emit('new_message', {
        senderId: socket.userId,
        message,
        type,
        timestamp: new Date()
      });
    });

    // Handle disconnect
    socket.on('disconnect', () => {
      console.log(`User ${socket.userId} disconnected`);
    });
  });

  // Helper functions to emit events from other parts of the application
  const emitToUser = (userId, event, data) => {
    io.to(`user_${userId}`).emit(event, data);
  };

  const emitToOrder = (orderId, event, data) => {
    io.to(`order_${orderId}`).emit(event, data);
  };

  const emitToVendors = (event, data) => {
    io.to('vendors').emit(event, data);
  };

  // Attach helper functions to io object for use in other modules
  io.emitToUser = emitToUser;
  io.emitToOrder = emitToOrder;
  io.emitToVendors = emitToVendors;
};

module.exports = socketHandler;
