import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../models/order_model.dart';
import '../models/cart_model.dart';
import 'realtime_notification_service.dart';

class RealtimeOrderService {
  static final RealtimeOrderService _instance = RealtimeOrderService._internal();
  factory RealtimeOrderService() => _instance;
  RealtimeOrderService._internal();

  final StreamController<OrderModel> _orderStreamController = StreamController<OrderModel>.broadcast();
  final StreamController<Map<String, dynamic>> _vendorNotificationController = StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<Map<String, dynamic>> _customerNotificationController = StreamController<Map<String, dynamic>>.broadcast();

  Stream<OrderModel> get orderStream => _orderStreamController.stream;
  Stream<Map<String, dynamic>> get vendorNotificationStream => _vendorNotificationController.stream;
  Stream<Map<String, dynamic>> get customerNotificationStream => _customerNotificationController.stream;

  final List<OrderModel> _orders = [];
  final RealtimeNotificationService _notificationService = RealtimeNotificationService();

  // Simulate real-time order placement
  Future<String> placeOrder({
    required String customerId,
    required String vendorId,
    required String vendorName,
    required List<CartItemModel> items,
    required String deliveryAddress,
    required Map<String, dynamic> location,
    required String paymentMethod,
    required double totalAmount,
  }) async {
    try {
      // Generate order ID
      final orderId = 'ORD${DateTime.now().millisecondsSinceEpoch}';

      // Create order
      final order = OrderModel(
        id: orderId,
        userId: customerId,
        vendorId: vendorId,
        vendorName: vendorName,
        items: items,
        deliveryAddress: deliveryAddress,
        location: location,
        totalAmount: totalAmount,
        status: 'placed',
        paymentStatus: paymentMethod == 'cod' ? 'pending' : 'paid',
        createdAt: DateTime.now(),
        deliveryEta: DateTime.now().add(const Duration(hours: 2)),
      );

      // Add to local storage
      _orders.add(order);

      // Emit order to stream
      _orderStreamController.add(order);

      // Send real-time notification to vendor
      await _sendVendorNotification(order);

      // Send confirmation to customer
      await _sendCustomerNotification(order, 'order_placed');

      // Simulate payment processing
      if (paymentMethod != 'cod') {
        await _processPayment(order);
      }

      if (kDebugMode) {
        print('🛒 Order placed successfully: $orderId');
        print('📱 Vendor notification sent');
        print('💳 Payment processing initiated');
      }

      return orderId;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error placing order: $e');
      }
      rethrow;
    }
  }

  // Update order status (called by vendor)
  Future<void> updateOrderStatus(String orderId, String newStatus, {String? note}) async {
    try {
      final orderIndex = _orders.indexWhere((order) => order.id == orderId);
      if (orderIndex == -1) {
        throw Exception('Order not found');
      }

      final order = _orders[orderIndex];
      final updatedOrder = order.copyWith(
        status: newStatus,
        updatedAt: DateTime.now(),
      );

      _orders[orderIndex] = updatedOrder;

      // Emit updated order
      _orderStreamController.add(updatedOrder);

      // Send notification to customer
      await _sendCustomerNotification(updatedOrder, 'status_update');

      // Handle specific status updates
      switch (newStatus) {
        case 'confirmed':
          await _handleOrderConfirmed(updatedOrder);
          break;
        case 'preparing':
          await _handleOrderPreparing(updatedOrder);
          break;
        case 'out_for_delivery':
          await _handleOrderOutForDelivery(updatedOrder);
          break;
        case 'delivered':
          await _handleOrderDelivered(updatedOrder);
          break;
        case 'cancelled':
          await _handleOrderCancelled(updatedOrder);
          break;
      }

      if (kDebugMode) {
        print('📦 Order $orderId status updated to: $newStatus');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error updating order status: $e');
      }
      rethrow;
    }
  }

  // Get orders for customer
  List<OrderModel> getCustomerOrders(String customerId) {
    return _orders.where((order) => order.userId == customerId).toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }

  // Get orders for vendor
  List<OrderModel> getVendorOrders(String vendorId) {
    return _orders.where((order) => order.vendorId == vendorId).toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }

  // Get order by ID
  OrderModel? getOrderById(String orderId) {
    try {
      return _orders.firstWhere((order) => order.id == orderId);
    } catch (e) {
      return null;
    }
  }

  // Private methods
  Future<void> _sendVendorNotification(OrderModel order) async {
    final notification = {
      'type': 'new_order',
      'orderId': order.id,
      'title': 'New Order Received! 🛒',
      'message': 'Order #${order.id.substring(order.id.length - 6)} - ₹${order.totalAmount.toStringAsFixed(0)}',
      'data': {
        'orderId': order.id,
        'customerId': order.userId,
        'totalAmount': order.totalAmount,
        'itemCount': order.items.length,
      },
      'timestamp': DateTime.now().toIso8601String(),
    };

    _vendorNotificationController.add(notification);
    
    // Send push notification
    await _notificationService.sendRealtimeNotification(
      userId: order.vendorId,
      title: notification['title'] as String,
      body: notification['message'] as String,
      data: notification['data'] as Map<String, dynamic>,
    );
  }

  Future<void> _sendCustomerNotification(OrderModel order, String type) async {
    String title = '';
    String message = '';

    switch (type) {
      case 'order_placed':
        title = 'Order Placed Successfully! ✅';
        message = 'Your order #${order.id.substring(order.id.length - 6)} has been placed';
        break;
      case 'status_update':
        title = _getStatusTitle(order.status);
        message = 'Order #${order.id.substring(order.id.length - 6)} is ${_getStatusMessage(order.status)}';
        break;
    }

    final notification = {
      'type': type,
      'orderId': order.id,
      'title': title,
      'message': message,
      'data': {
        'orderId': order.id,
        'status': order.status,
        'vendorId': order.vendorId,
      },
      'timestamp': DateTime.now().toIso8601String(),
    };

    _customerNotificationController.add(notification);

    // Send push notification
    await _notificationService.sendRealtimeNotification(
      userId: order.userId,
      title: title,
      body: message,
      data: notification['data'] as Map<String, dynamic>,
    );
  }

  Future<void> _processPayment(OrderModel order) async {
    // Simulate payment processing delay
    await Future.delayed(const Duration(seconds: 2));
    
    // In a real app, integrate with Razorpay/Stripe here
    if (kDebugMode) {
      print('💳 Payment processed for order: ${order.id}');
      print('💰 Amount: ₹${order.totalAmount}');
      print('🔄 Method: ${order.paymentStatus}');
    }
  }

  Future<void> _handleOrderConfirmed(OrderModel order) async {
    if (kDebugMode) {
      print('✅ Order confirmed: ${order.id}');
    }
  }

  Future<void> _handleOrderPreparing(OrderModel order) async {
    if (kDebugMode) {
      print('👨‍🍳 Order being prepared: ${order.id}');
    }
  }

  Future<void> _handleOrderOutForDelivery(OrderModel order) async {
    // Generate delivery OTP
    final otp = _generateDeliveryOTP();
    
    // Send OTP to customer
    await _sendCustomerNotification(
      order,
      'delivery_otp'
    );

    if (kDebugMode) {
      print('🚚 Order out for delivery: ${order.id}');
      print('🔐 Delivery OTP: $otp');
    }
  }

  Future<void> _handleOrderDelivered(OrderModel order) async {
    if (kDebugMode) {
      print('📦 Order delivered: ${order.id}');
    }
  }

  Future<void> _handleOrderCancelled(OrderModel order) async {
    if (kDebugMode) {
      print('❌ Order cancelled: ${order.id}');
    }
  }

  String _getStatusTitle(String status) {
    switch (status) {
      case 'confirmed':
        return 'Order Confirmed! ✅';
      case 'preparing':
        return 'Order Being Prepared 👨‍🍳';
      case 'out_for_delivery':
        return 'Out for Delivery! 🚚';
      case 'delivered':
        return 'Order Delivered! 📦';
      case 'cancelled':
        return 'Order Cancelled ❌';
      default:
        return 'Order Update';
    }
  }

  String _getStatusMessage(String status) {
    switch (status) {
      case 'confirmed':
        return 'confirmed and will be prepared soon';
      case 'preparing':
        return 'being prepared by the vendor';
      case 'out_for_delivery':
        return 'on the way to your location';
      case 'delivered':
        return 'delivered successfully';
      case 'cancelled':
        return 'cancelled';
      default:
        return 'updated';
    }
  }

  String _generateDeliveryOTP() {
    return (1000 + (DateTime.now().millisecondsSinceEpoch % 9000)).toString();
  }

  // Cleanup
  void dispose() {
    _orderStreamController.close();
    _vendorNotificationController.close();
    _customerNotificationController.close();
  }
}
