class LocationModel {
  final double latitude;
  final double longitude;
  final String address;
  final String city;
  final String state;
  final String pincode;
  final String? landmark;
  final String? locationType; // home, office, other
  final bool isDefault;

  LocationModel({
    required this.latitude,
    required this.longitude,
    required this.address,
    required this.city,
    required this.state,
    required this.pincode,
    this.landmark,
    this.locationType,
    this.isDefault = false,
  });

  // Create from map
  factory LocationModel.fromMap(Map<String, dynamic> map) {
    return LocationModel(
      latitude: map['latitude']?.toDouble() ?? 0.0,
      longitude: map['longitude']?.toDouble() ?? 0.0,
      address: map['address'] ?? '',
      city: map['city'] ?? '',
      state: map['state'] ?? '',
      pincode: map['pincode'] ?? '',
      landmark: map['landmark'],
      locationType: map['locationType'],
      isDefault: map['isDefault'] ?? false,
    );
  }

  // Convert to map
  Map<String, dynamic> toMap() {
    return {
      'latitude': latitude,
      'longitude': longitude,
      'address': address,
      'city': city,
      'state': state,
      'pincode': pincode,
      'landmark': landmark,
      'locationType': locationType,
      'isDefault': isDefault,
    };
  }

  // Create copy with modifications
  LocationModel copyWith({
    double? latitude,
    double? longitude,
    String? address,
    String? city,
    String? state,
    String? pincode,
    String? landmark,
    String? locationType,
    bool? isDefault,
  }) {
    return LocationModel(
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      address: address ?? this.address,
      city: city ?? this.city,
      state: state ?? this.state,
      pincode: pincode ?? this.pincode,
      landmark: landmark ?? this.landmark,
      locationType: locationType ?? this.locationType,
      isDefault: isDefault ?? this.isDefault,
    );
  }

  // Get formatted address
  String get formattedAddress {
    String formatted = address;
    if (landmark != null && landmark!.isNotEmpty) {
      formatted += ', $landmark';
    }
    return formatted;
  }

  // Get short address
  String get shortAddress {
    List<String> parts = address.split(',');
    if (parts.length > 2) {
      return '${parts[0]}, ${parts[1]}';
    }
    return address;
  }

  // Get location type icon
  String get locationIcon {
    switch (locationType?.toLowerCase()) {
      case 'home':
        return '🏠';
      case 'office':
        return '🏢';
      case 'work':
        return '💼';
      default:
        return '📍';
    }
  }

  @override
  String toString() {
    return 'LocationModel(latitude: $latitude, longitude: $longitude, address: $address, city: $city, state: $state, pincode: $pincode)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LocationModel &&
        other.latitude == latitude &&
        other.longitude == longitude &&
        other.address == address &&
        other.city == city &&
        other.state == state &&
        other.pincode == pincode;
  }

  @override
  int get hashCode {
    return latitude.hashCode ^
        longitude.hashCode ^
        address.hashCode ^
        city.hashCode ^
        state.hashCode ^
        pincode.hashCode;
  }
}
