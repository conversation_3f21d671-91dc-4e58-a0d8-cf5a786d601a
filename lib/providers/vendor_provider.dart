import 'package:flutter/material.dart';
import '../models/vendor_model.dart';
import '../models/location_model.dart';
import '../services/sample_data_service.dart';

class VendorProvider extends ChangeNotifier {
  List<VendorModel> _vendors = [];
  List<VendorModel> _nearbyVendors = [];
  List<VendorModel> _filteredVendors = [];
  VendorModel? _selectedVendor;
  bool _isLoading = false;
  String? _errorMessage;
  
  // Filter options
  String _selectedCategory = '';
  double _minRating = 0.0;
  double _maxDistance = 10.0;
  String _priceRange = '';
  bool _openNow = false;
  String _searchQuery = '';

  // Getters
  List<VendorModel> get vendors => _vendors;
  List<VendorModel> get nearbyVendors => _nearbyVendors;
  List<VendorModel> get filteredVendors => _filteredVendors;
  VendorModel? get selectedVendor => _selectedVendor;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  String get selectedCategory => _selectedCategory;
  double get minRating => _minRating;
  double get maxDistance => _maxDistance;
  String get priceRange => _priceRange;
  bool get openNow => _openNow;
  String get searchQuery => _searchQuery;

  // Load all vendors
  Future<void> loadVendors() async {
    _setLoading(true);
    _clearError();

    try {
      await Future.delayed(const Duration(milliseconds: 500));
      _vendors = SampleDataService.getSampleVendors();
      _filteredVendors = List.from(_vendors);
      _setLoading(false);
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
    }
  }

  // Load nearby vendors based on location
  Future<void> loadNearbyVendors(LocationModel userLocation, {double radiusKm = 10.0}) async {
    _setLoading(true);
    _clearError();

    try {
      await Future.delayed(const Duration(milliseconds: 500));
      
      // Get all vendors and calculate distances
      List<VendorModel> allVendors = SampleDataService.getSampleVendors();
      
      _nearbyVendors = allVendors.map((vendor) {
        double distance = _calculateDistance(userLocation, vendor.location);
        return vendor.copyWith(distance: distance);
      }).where((vendor) => vendor.distance! <= radiusKm)
        .toList();

      // Sort by distance
      _nearbyVendors.sort((a, b) => a.distance!.compareTo(b.distance!));
      
      _setLoading(false);
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
    }
  }

  // Search vendors
  void searchVendors(String query) {
    _searchQuery = query;
    _applyFilters();
  }

  // Set category filter
  void setCategoryFilter(String category) {
    _selectedCategory = category;
    _applyFilters();
  }

  // Set rating filter
  void setRatingFilter(double minRating) {
    _minRating = minRating;
    _applyFilters();
  }

  // Set distance filter
  void setDistanceFilter(double maxDistance) {
    _maxDistance = maxDistance;
    _applyFilters();
  }

  // Set price range filter
  void setPriceRangeFilter(String priceRange) {
    _priceRange = priceRange;
    _applyFilters();
  }

  // Set open now filter
  void setOpenNowFilter(bool openNow) {
    _openNow = openNow;
    _applyFilters();
  }

  // Apply all filters
  void _applyFilters() {
    List<VendorModel> filtered = List.from(_vendors);

    // Search filter
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((vendor) =>
          vendor.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          vendor.businessName?.toLowerCase().contains(_searchQuery.toLowerCase()) == true ||
          vendor.specializations?.any((spec) => 
              spec.toLowerCase().contains(_searchQuery.toLowerCase())) == true
      ).toList();
    }

    // Category filter
    if (_selectedCategory.isNotEmpty) {
      filtered = filtered.where((vendor) =>
          vendor.specializations?.any((spec) => 
              spec.toLowerCase() == _selectedCategory.toLowerCase()) == true
      ).toList();
    }

    // Rating filter
    if (_minRating > 0) {
      filtered = filtered.where((vendor) => vendor.rating >= _minRating).toList();
    }

    // Distance filter
    if (_maxDistance < 50) {
      filtered = filtered.where((vendor) => 
          vendor.distance == null || vendor.distance! <= _maxDistance).toList();
    }

    // Open now filter
    if (_openNow) {
      filtered = filtered.where((vendor) => vendor.isAvailable).toList();
    }

    // Price range filter
    if (_priceRange.isNotEmpty) {
      filtered = _filterByPriceRange(filtered, _priceRange);
    }

    _filteredVendors = filtered;
    notifyListeners();
  }

  // Filter by price range
  List<VendorModel> _filterByPriceRange(List<VendorModel> vendors, String priceRange) {
    switch (priceRange.toLowerCase()) {
      case 'budget':
        return vendors.where((vendor) => vendor.averagePrice <= 500).toList();
      case 'mid-range':
        return vendors.where((vendor) => 
            vendor.averagePrice > 500 && vendor.averagePrice <= 1000).toList();
      case 'premium':
        return vendors.where((vendor) => vendor.averagePrice > 1000).toList();
      default:
        return vendors;
    }
  }

  // Clear all filters
  void clearFilters() {
    _selectedCategory = '';
    _minRating = 0.0;
    _maxDistance = 10.0;
    _priceRange = '';
    _openNow = false;
    _searchQuery = '';
    _filteredVendors = List.from(_vendors);
    notifyListeners();
  }

  // Select vendor
  void selectVendor(VendorModel vendor) {
    _selectedVendor = vendor;
    notifyListeners();
  }

  // Get vendor by ID
  VendorModel? getVendorById(String vendorId) {
    try {
      return _vendors.firstWhere((vendor) => vendor.id == vendorId);
    } catch (e) {
      return null;
    }
  }

  // Get vendors by category
  List<VendorModel> getVendorsByCategory(String category) {
    return _vendors.where((vendor) =>
        vendor.specializations?.any((spec) => 
            spec.toLowerCase() == category.toLowerCase()) == true
    ).toList();
  }

  // Get top rated vendors
  List<VendorModel> getTopRatedVendors({int limit = 10}) {
    List<VendorModel> sorted = List.from(_vendors);
    sorted.sort((a, b) => b.rating.compareTo(a.rating));
    return sorted.take(limit).toList();
  }

  // Get vendors with fastest delivery
  List<VendorModel> getFastestDeliveryVendors({int limit = 10}) {
    List<VendorModel> sorted = List.from(_vendors);
    sorted.sort((a, b) => a.averageDeliveryTime.compareTo(b.averageDeliveryTime));
    return sorted.take(limit).toList();
  }

  // Calculate distance between two locations
  double _calculateDistance(LocationModel from, LocationModel to) {
    // Simplified distance calculation (in km)
    const double earthRadius = 6371;
    
    double lat1Rad = from.latitude * (3.14159 / 180);
    double lat2Rad = to.latitude * (3.14159 / 180);
    double deltaLatRad = (to.latitude - from.latitude) * (3.14159 / 180);
    double deltaLonRad = (to.longitude - from.longitude) * (3.14159 / 180);

    double a = (deltaLatRad / 2).abs() * (deltaLatRad / 2).abs() +
        (lat1Rad).abs() * (lat2Rad).abs() *
        (deltaLonRad / 2).abs() * (deltaLonRad / 2).abs();
    
    double c = 2 * (a.abs()).abs();
    
    return earthRadius * c;
  }

  // Get delivery time estimate
  String getDeliveryTimeEstimate(VendorModel vendor, LocationModel userLocation) {
    if (vendor.distance != null) {
      if (vendor.distance! <= 2) {
        return '30-45 mins';
      } else if (vendor.distance! <= 5) {
        return '45-60 mins';
      } else if (vendor.distance! <= 10) {
        return '1-2 hours';
      } else {
        return '2-4 hours';
      }
    }
    return '${vendor.averageDeliveryTime} mins';
  }

  // Check if vendor delivers to location
  bool doesVendorDeliverTo(VendorModel vendor, LocationModel location) {
    if (vendor.distance != null) {
      return vendor.distance! <= vendor.deliveryRadius;
    }
    return true; // Default to true if distance not calculated
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }
}
