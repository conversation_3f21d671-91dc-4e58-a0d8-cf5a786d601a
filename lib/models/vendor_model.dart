import 'location_model.dart';

class VendorModel {
  final String id;
  final String name;
  final String? businessName;
  final String? profileImage;
  final String? businessLicense;
  final List<String>? specializations;
  final LocationModel location;
  final String phone;
  final String? email;
  final double rating;
  final int reviewCount;
  final bool isAvailable;
  final bool isVerified;
  final double averagePrice;
  final int averageDeliveryTime; // in minutes
  final double deliveryRadius; // in km
  final double? distance; // distance from user location
  final String? businessHours;
  final List<String>? deliveryAreas;
  final double minimumOrderValue;
  final bool freeDeliveryAvailable;
  final double? freeDeliveryThreshold;
  final DateTime createdAt;
  final DateTime? lastActive;

  VendorModel({
    required this.id,
    required this.name,
    this.businessName,
    this.profileImage,
    this.businessLicense,
    this.specializations,
    required this.location,
    required this.phone,
    this.email,
    required this.rating,
    required this.reviewCount,
    required this.isAvailable,
    required this.isVerified,
    required this.averagePrice,
    required this.averageDeliveryTime,
    required this.deliveryRadius,
    this.distance,
    this.businessHours,
    this.deliveryAreas,
    required this.minimumOrderValue,
    required this.freeDeliveryAvailable,
    this.freeDeliveryThreshold,
    required this.createdAt,
    this.lastActive,
  });

  // Create from map
  factory VendorModel.fromMap(Map<String, dynamic> map) {
    return VendorModel(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      businessName: map['businessName'],
      profileImage: map['profileImage'],
      businessLicense: map['businessLicense'],
      specializations: map['specializations'] != null 
          ? List<String>.from(map['specializations']) 
          : null,
      location: LocationModel.fromMap(map['location'] ?? {}),
      phone: map['phone'] ?? '',
      email: map['email'],
      rating: map['rating']?.toDouble() ?? 0.0,
      reviewCount: map['reviewCount'] ?? 0,
      isAvailable: map['isAvailable'] ?? false,
      isVerified: map['isVerified'] ?? false,
      averagePrice: map['averagePrice']?.toDouble() ?? 0.0,
      averageDeliveryTime: map['averageDeliveryTime'] ?? 60,
      deliveryRadius: map['deliveryRadius']?.toDouble() ?? 10.0,
      distance: map['distance']?.toDouble(),
      businessHours: map['businessHours'],
      deliveryAreas: map['deliveryAreas'] != null 
          ? List<String>.from(map['deliveryAreas']) 
          : null,
      minimumOrderValue: map['minimumOrderValue']?.toDouble() ?? 0.0,
      freeDeliveryAvailable: map['freeDeliveryAvailable'] ?? false,
      freeDeliveryThreshold: map['freeDeliveryThreshold']?.toDouble(),
      createdAt: DateTime.parse(map['createdAt'] ?? DateTime.now().toIso8601String()),
      lastActive: map['lastActive'] != null 
          ? DateTime.parse(map['lastActive']) 
          : null,
    );
  }

  // Convert to map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'businessName': businessName,
      'profileImage': profileImage,
      'businessLicense': businessLicense,
      'specializations': specializations,
      'location': location.toMap(),
      'phone': phone,
      'email': email,
      'rating': rating,
      'reviewCount': reviewCount,
      'isAvailable': isAvailable,
      'isVerified': isVerified,
      'averagePrice': averagePrice,
      'averageDeliveryTime': averageDeliveryTime,
      'deliveryRadius': deliveryRadius,
      'distance': distance,
      'businessHours': businessHours,
      'deliveryAreas': deliveryAreas,
      'minimumOrderValue': minimumOrderValue,
      'freeDeliveryAvailable': freeDeliveryAvailable,
      'freeDeliveryThreshold': freeDeliveryThreshold,
      'createdAt': createdAt.toIso8601String(),
      'lastActive': lastActive?.toIso8601String(),
    };
  }

  // Create copy with modifications
  VendorModel copyWith({
    String? id,
    String? name,
    String? businessName,
    String? profileImage,
    String? businessLicense,
    List<String>? specializations,
    LocationModel? location,
    String? phone,
    String? email,
    double? rating,
    int? reviewCount,
    bool? isAvailable,
    bool? isVerified,
    double? averagePrice,
    int? averageDeliveryTime,
    double? deliveryRadius,
    double? distance,
    String? businessHours,
    List<String>? deliveryAreas,
    double? minimumOrderValue,
    bool? freeDeliveryAvailable,
    double? freeDeliveryThreshold,
    DateTime? createdAt,
    DateTime? lastActive,
  }) {
    return VendorModel(
      id: id ?? this.id,
      name: name ?? this.name,
      businessName: businessName ?? this.businessName,
      profileImage: profileImage ?? this.profileImage,
      businessLicense: businessLicense ?? this.businessLicense,
      specializations: specializations ?? this.specializations,
      location: location ?? this.location,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      rating: rating ?? this.rating,
      reviewCount: reviewCount ?? this.reviewCount,
      isAvailable: isAvailable ?? this.isAvailable,
      isVerified: isVerified ?? this.isVerified,
      averagePrice: averagePrice ?? this.averagePrice,
      averageDeliveryTime: averageDeliveryTime ?? this.averageDeliveryTime,
      deliveryRadius: deliveryRadius ?? this.deliveryRadius,
      distance: distance ?? this.distance,
      businessHours: businessHours ?? this.businessHours,
      deliveryAreas: deliveryAreas ?? this.deliveryAreas,
      minimumOrderValue: minimumOrderValue ?? this.minimumOrderValue,
      freeDeliveryAvailable: freeDeliveryAvailable ?? this.freeDeliveryAvailable,
      freeDeliveryThreshold: freeDeliveryThreshold ?? this.freeDeliveryThreshold,
      createdAt: createdAt ?? this.createdAt,
      lastActive: lastActive ?? this.lastActive,
    );
  }

  // Get display name
  String get displayName => businessName ?? name;

  // Get primary specialization
  String get primarySpecialization => 
      specializations?.isNotEmpty == true ? specializations!.first : 'General';

  // Get price range indicator
  String get priceRangeIndicator {
    if (averagePrice <= 500) {
      return '₹';
    } else if (averagePrice <= 1000) {
      return '₹₹';
    } else {
      return '₹₹₹';
    }
  }

  // Get delivery time text
  String get deliveryTimeText {
    if (averageDeliveryTime <= 30) {
      return 'Express delivery';
    } else if (averageDeliveryTime <= 60) {
      return '${averageDeliveryTime} mins';
    } else {
      int hours = (averageDeliveryTime / 60).floor();
      int mins = averageDeliveryTime % 60;
      if (mins == 0) {
        return '${hours}h';
      } else {
        return '${hours}h ${mins}m';
      }
    }
  }

  // Get distance text
  String get distanceText {
    if (distance == null) return '';
    if (distance! < 1) {
      return '${(distance! * 1000).round()}m away';
    } else {
      return '${distance!.toStringAsFixed(1)} km away';
    }
  }

  // Check if currently open
  bool get isCurrentlyOpen {
    if (!isAvailable) return false;
    if (businessHours == null) return true;
    
    // Simple check - in real app, parse business hours properly
    DateTime now = DateTime.now();
    int currentHour = now.hour;
    
    // Assume most vendors are open 9 AM to 6 PM
    return currentHour >= 9 && currentHour < 18;
  }

  // Get status text
  String get statusText {
    if (!isAvailable) return 'Closed';
    if (isCurrentlyOpen) return 'Open';
    return 'Closed';
  }

  // Get verification badge text
  String get verificationText {
    return isVerified ? 'Verified' : 'Not Verified';
  }

  @override
  String toString() {
    return 'VendorModel(id: $id, name: $name, businessName: $businessName, rating: $rating, isAvailable: $isAvailable)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is VendorModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
