const twilio = require('twilio');

// Initialize Twilio client only if credentials are provided
let client = null;
if (process.env.TWILIO_ACCOUNT_SID &&
    process.env.TWILIO_AUTH_TOKEN &&
    process.env.TWILIO_ACCOUNT_SID.startsWith('AC')) {
  client = twilio(
    process.env.TWILIO_ACCOUNT_SID,
    process.env.TWILIO_AUTH_TOKEN
  );
}

/**
 * Send OTP via SMS
 * @param {string} phone - Phone number with country code
 * @param {string} otp - OTP code
 * @returns {Promise<object>} - Twilio response
 */
const sendOTP = async (phone, otp) => {
  try {
    if (!client) {
      console.log(`[DEMO MODE] OTP for ${phone}: ${otp}`);
      return { sid: 'demo_message_id', status: 'sent' };
    }

    const message = `Your BuildBid verification code is: ${otp}. Valid for 10 minutes. Do not share this code with anyone.`;

    const result = await client.messages.create({
      body: message,
      from: process.env.TWILIO_PHONE_NUMBER,
      to: phone
    });

    console.log(`OTP sent to ${phone}: ${result.sid}`);
    return result;
  } catch (error) {
    console.error('SMS sending failed:', error);
    throw new Error('Failed to send OTP');
  }
};

/**
 * Send order confirmation SMS
 * @param {string} phone - Phone number
 * @param {object} orderData - Order details
 * @returns {Promise<object>} - Twilio response
 */
const sendOrderConfirmation = async (phone, orderData) => {
  try {
    const message = `Order confirmed! Order #${orderData.orderNumber} for ₹${orderData.total} will be delivered to ${orderData.address}. Track: ${process.env.APP_URL}/orders/${orderData.orderId}`;
    
    const result = await client.messages.create({
      body: message,
      from: process.env.TWILIO_PHONE_NUMBER,
      to: phone
    });

    console.log(`Order confirmation sent to ${phone}: ${result.sid}`);
    return result;
  } catch (error) {
    console.error('Order confirmation SMS failed:', error);
    throw new Error('Failed to send order confirmation');
  }
};

/**
 * Send delivery notification SMS
 * @param {string} phone - Phone number
 * @param {object} deliveryData - Delivery details
 * @returns {Promise<object>} - Twilio response
 */
const sendDeliveryNotification = async (phone, deliveryData) => {
  try {
    const message = `Your order #${deliveryData.orderNumber} is out for delivery! Delivery person: ${deliveryData.deliveryPerson}, Phone: ${deliveryData.deliveryPhone}. OTP: ${deliveryData.otp}`;
    
    const result = await client.messages.create({
      body: message,
      from: process.env.TWILIO_PHONE_NUMBER,
      to: phone
    });

    console.log(`Delivery notification sent to ${phone}: ${result.sid}`);
    return result;
  } catch (error) {
    console.error('Delivery notification SMS failed:', error);
    throw new Error('Failed to send delivery notification');
  }
};

/**
 * Send service booking confirmation SMS
 * @param {string} phone - Phone number
 * @param {object} bookingData - Booking details
 * @returns {Promise<object>} - Twilio response
 */
const sendServiceBookingConfirmation = async (phone, bookingData) => {
  try {
    const message = `Service booked! ${bookingData.serviceName} with ${bookingData.providerName} on ${bookingData.date} at ${bookingData.time}. Contact: ${bookingData.providerPhone}`;
    
    const result = await client.messages.create({
      body: message,
      from: process.env.TWILIO_PHONE_NUMBER,
      to: phone
    });

    console.log(`Service booking confirmation sent to ${phone}: ${result.sid}`);
    return result;
  } catch (error) {
    console.error('Service booking confirmation SMS failed:', error);
    throw new Error('Failed to send service booking confirmation');
  }
};

/**
 * Send payment reminder SMS
 * @param {string} phone - Phone number
 * @param {object} paymentData - Payment details
 * @returns {Promise<object>} - Twilio response
 */
const sendPaymentReminder = async (phone, paymentData) => {
  try {
    const message = `Payment reminder: Order #${paymentData.orderNumber} amount ₹${paymentData.amount} is pending. Pay now: ${process.env.APP_URL}/payment/${paymentData.paymentId}`;
    
    const result = await client.messages.create({
      body: message,
      from: process.env.TWILIO_PHONE_NUMBER,
      to: phone
    });

    console.log(`Payment reminder sent to ${phone}: ${result.sid}`);
    return result;
  } catch (error) {
    console.error('Payment reminder SMS failed:', error);
    throw new Error('Failed to send payment reminder');
  }
};

/**
 * Send promotional SMS
 * @param {string} phone - Phone number
 * @param {object} promoData - Promotion details
 * @returns {Promise<object>} - Twilio response
 */
const sendPromotionalSMS = async (phone, promoData) => {
  try {
    const message = `${promoData.title} - ${promoData.description}. Use code: ${promoData.code}. Valid till ${promoData.validTill}. Shop now: ${process.env.APP_URL}`;
    
    const result = await client.messages.create({
      body: message,
      from: process.env.TWILIO_PHONE_NUMBER,
      to: phone
    });

    console.log(`Promotional SMS sent to ${phone}: ${result.sid}`);
    return result;
  } catch (error) {
    console.error('Promotional SMS failed:', error);
    throw new Error('Failed to send promotional SMS');
  }
};

/**
 * Send bulk SMS to multiple recipients
 * @param {Array<string>} phones - Array of phone numbers
 * @param {string} message - Message content
 * @returns {Promise<Array>} - Array of results
 */
const sendBulkSMS = async (phones, message) => {
  try {
    const promises = phones.map(phone => 
      client.messages.create({
        body: message,
        from: process.env.TWILIO_PHONE_NUMBER,
        to: phone
      })
    );

    const results = await Promise.allSettled(promises);
    
    const successful = results.filter(result => result.status === 'fulfilled').length;
    const failed = results.filter(result => result.status === 'rejected').length;
    
    console.log(`Bulk SMS: ${successful} successful, ${failed} failed`);
    
    return {
      successful,
      failed,
      results
    };
  } catch (error) {
    console.error('Bulk SMS failed:', error);
    throw new Error('Failed to send bulk SMS');
  }
};

/**
 * Validate phone number format
 * @param {string} phone - Phone number
 * @returns {boolean} - Is valid
 */
const validatePhoneNumber = (phone) => {
  const phoneRegex = /^\+?[1-9]\d{1,14}$/;
  return phoneRegex.test(phone);
};

/**
 * Format phone number to E.164 format
 * @param {string} phone - Phone number
 * @param {string} countryCode - Default country code (default: +91 for India)
 * @returns {string} - Formatted phone number
 */
const formatPhoneNumber = (phone, countryCode = '+91') => {
  // Remove all non-digit characters
  const cleaned = phone.replace(/\D/g, '');
  
  // If already has country code
  if (cleaned.length > 10) {
    return '+' + cleaned;
  }
  
  // Add country code
  return countryCode + cleaned;
};

/**
 * Get SMS delivery status
 * @param {string} messageSid - Twilio message SID
 * @returns {Promise<object>} - Message status
 */
const getSMSStatus = async (messageSid) => {
  try {
    const message = await client.messages(messageSid).fetch();
    return {
      sid: message.sid,
      status: message.status,
      errorCode: message.errorCode,
      errorMessage: message.errorMessage,
      dateCreated: message.dateCreated,
      dateSent: message.dateSent,
      dateUpdated: message.dateUpdated
    };
  } catch (error) {
    console.error('Failed to get SMS status:', error);
    throw new Error('Failed to get SMS status');
  }
};

module.exports = {
  sendOTP,
  sendOrderConfirmation,
  sendDeliveryNotification,
  sendServiceBookingConfirmation,
  sendPaymentReminder,
  sendPromotionalSMS,
  sendBulkSMS,
  validatePhoneNumber,
  formatPhoneNumber,
  getSMSStatus
};
