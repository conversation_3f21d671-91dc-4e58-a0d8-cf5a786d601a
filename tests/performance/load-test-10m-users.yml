# 🚀 BuildBid Load Testing Configuration
# Simulates 10M users with realistic usage patterns

config:
  target: 'https://api.buildbid.com'
  phases:
    # Warm-up phase
    - duration: 300  # 5 minutes
      arrivalRate: 10
      name: "Warm-up"
    
    # Gradual ramp-up to 1M users
    - duration: 1800  # 30 minutes
      arrivalRate: 100
      rampTo: 1000
      name: "Ramp-up to 1M users"
    
    # Sustained load - 5M users
    - duration: 3600  # 1 hour
      arrivalRate: 5000
      name: "Sustained 5M users"
    
    # Peak load - 10M users
    - duration: 1800  # 30 minutes
      arrivalRate: 10000
      name: "Peak 10M users"
    
    # Cool down
    - duration: 600  # 10 minutes
      arrivalRate: 10000
      rampTo: 100
      name: "Cool down"

  payload:
    path: "./test-data.csv"
    fields:
      - "userId"
      - "userRole"
      - "location"
      - "phone"

  plugins:
    metrics-by-endpoint:
      useOnlyRequestNames: true
    
scenarios:
  # User Authentication Flow (30% of traffic)
  - name: "User Authentication"
    weight: 30
    flow:
      - post:
          url: "/api/auth/send-otp"
          json:
            phone: "{{ phone }}"
            role: "{{ userRole }}"
          capture:
            - json: "$.verificationId"
              as: "verificationId"
      
      - think: 30  # User takes 30s to receive and enter OTP
      
      - post:
          url: "/api/auth/verify-otp"
          json:
            verificationId: "{{ verificationId }}"
            otp: "123456"
            phone: "{{ phone }}"
          capture:
            - json: "$.token"
              as: "authToken"
            - json: "$.user.id"
              as: "userId"

  # Browse Products/Materials (40% of traffic)
  - name: "Browse Products"
    weight: 40
    flow:
      - get:
          url: "/api/products"
          qs:
            location: "{{ location }}"
            category: "cement"
            page: 1
            limit: 20
          headers:
            Authorization: "Bearer {{ authToken }}"
      
      - think: 5
      
      - get:
          url: "/api/products/{{ $randomInt(1, 1000) }}"
          headers:
            Authorization: "Bearer {{ authToken }}"
      
      - think: 10
      
      - get:
          url: "/api/vendors/nearby"
          qs:
            lat: 28.6139
            lng: 77.2090
            radius: 10
          headers:
            Authorization: "Bearer {{ authToken }}"

  # Place Order Flow (20% of traffic)
  - name: "Place Order"
    weight: 20
    flow:
      - post:
          url: "/api/cart/add"
          json:
            productId: "{{ $randomInt(1, 1000) }}"
            quantity: "{{ $randomInt(1, 100) }}"
            vendorId: "{{ $randomInt(1, 100) }}"
          headers:
            Authorization: "Bearer {{ authToken }}"
      
      - think: 30  # User reviews cart
      
      - post:
          url: "/api/orders"
          json:
            items:
              - productId: "{{ $randomInt(1, 1000) }}"
                quantity: "{{ $randomInt(1, 100) }}"
                price: "{{ $randomInt(100, 10000) }}"
            deliveryAddress: "Test Address, {{ location }}"
            paymentMethod: "razorpay"
          headers:
            Authorization: "Bearer {{ authToken }}"
          capture:
            - json: "$.orderId"
              as: "orderId"
      
      - think: 5
      
      - get:
          url: "/api/orders/{{ orderId }}"
          headers:
            Authorization: "Bearer {{ authToken }}"

  # Vendor Operations (15% of traffic)
  - name: "Vendor Operations"
    weight: 15
    flow:
      - get:
          url: "/api/vendor/dashboard"
          headers:
            Authorization: "Bearer {{ authToken }}"
      
      - get:
          url: "/api/vendor/orders"
          qs:
            status: "pending"
            page: 1
            limit: 10
          headers:
            Authorization: "Bearer {{ authToken }}"
      
      - think: 10
      
      - put:
          url: "/api/orders/{{ $randomInt(1, 10000) }}/status"
          json:
            status: "confirmed"
            estimatedDelivery: "2024-01-15T10:00:00Z"
          headers:
            Authorization: "Bearer {{ authToken }}"

  # Search and Filters (10% of traffic)
  - name: "Search Operations"
    weight: 10
    flow:
      - get:
          url: "/api/search"
          qs:
            q: "cement"
            location: "{{ location }}"
            priceMin: 100
            priceMax: 1000
            sortBy: "price"
            page: 1
          headers:
            Authorization: "Bearer {{ authToken }}"
      
      - think: 5
      
      - get:
          url: "/api/search/suggestions"
          qs:
            q: "cem"
          headers:
            Authorization: "Bearer {{ authToken }}"

  # Real-time Features (5% of traffic)
  - name: "Real-time Features"
    weight: 5
    flow:
      - get:
          url: "/api/notifications"
          headers:
            Authorization: "Bearer {{ authToken }}"
      
      - get:
          url: "/api/orders/{{ $randomInt(1, 10000) }}/tracking"
          headers:
            Authorization: "Bearer {{ authToken }}"
      
      - think: 30
      
      - post:
          url: "/api/chat/messages"
          json:
            recipientId: "{{ $randomInt(1, 1000) }}"
            message: "Hello, is the order ready?"
            type: "text"
          headers:
            Authorization: "Bearer {{ authToken }}"

# Performance expectations
expect:
  # API Response Times
  - statusCode: 200
  - contentType: json
  - hasProperty: success
  
  # Performance thresholds
  - maxErrorRate: 1  # Max 1% error rate
  - p95: 500  # 95th percentile under 500ms
  - p99: 1000  # 99th percentile under 1s

# Custom metrics
metrics:
  - name: "authentication_success_rate"
    unit: "percent"
  - name: "order_completion_rate"
    unit: "percent"
  - name: "search_response_time"
    unit: "ms"
  - name: "database_query_time"
    unit: "ms"

# Error handling
on:
  error:
    - log: "Error occurred: {{ error }}"
  
  response:
    - log: "Response time: {{ response.timings.response }}ms"

# Reporting
reporting:
  - type: "json"
    output: "./results/load-test-results.json"
  - type: "html"
    output: "./results/load-test-report.html"
