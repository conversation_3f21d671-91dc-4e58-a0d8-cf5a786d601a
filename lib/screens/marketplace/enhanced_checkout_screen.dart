import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../providers/order_provider.dart';
import '../../providers/notification_provider.dart';
import '../../models/cart_model.dart';
import '../../models/vendor_model.dart';
import '../../services/realtime_order_service.dart';
import '../../utils/app_colors.dart';
import '../../widgets/custom_button.dart';
import 'order_tracking_screen.dart';

class EnhancedCheckoutScreen extends StatefulWidget {
  final VendorModel vendor;
  final Map<String, int> cart;
  final List<dynamic> materials;
  final double total;

  const EnhancedCheckoutScreen({
    Key? key,
    required this.vendor,
    required this.cart,
    required this.materials,
    required this.total,
  }) : super(key: key);

  @override
  State<EnhancedCheckoutScreen> createState() => _EnhancedCheckoutScreenState();
}

class _EnhancedCheckoutScreenState extends State<EnhancedCheckoutScreen> {
  final _addressController = TextEditingController();
  final _notesController = TextEditingController();
  String _selectedPaymentMethod = 'cod';
  String _selectedTimeSlot = 'morning';
  bool _isPlacingOrder = false;

  final RealtimeOrderService _orderService = RealtimeOrderService();

  @override
  void dispose() {
    _addressController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Checkout'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Order Summary
            _buildOrderSummary(),
            
            // Delivery Address
            _buildDeliveryAddress(),
            
            // Time Slot Selection
            _buildTimeSlotSelection(),
            
            // Payment Method
            _buildPaymentMethod(),
            
            // Order Notes
            _buildOrderNotes(),
            
            // Price Breakdown
            _buildPriceBreakdown(),
            
            const SizedBox(height: 20),
          ],
        ),
      ),
      bottomNavigationBar: _buildBottomBar(),
    );
  }

  Widget _buildOrderSummary() {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.store, color: AppColors.primary),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    widget.vendor.businessName,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...widget.cart.entries.map((entry) {
              final material = widget.materials.firstWhere(
                (m) => m['id'] == entry.key,
                orElse: () => {'name': 'Unknown', 'price': 0, 'unit': 'piece'},
              );
              final quantity = entry.value;
              final price = material['price'] * quantity;
              
              return Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Row(
                  children: [
                    Expanded(
                      child: Text('${material['name']} x $quantity'),
                    ),
                    Text(
                      '₹$price',
                      style: const TextStyle(fontWeight: FontWeight.w600),
                    ),
                  ],
                ),
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildDeliveryAddress() {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.location_on, color: AppColors.primary),
                const SizedBox(width: 8),
                const Text(
                  'Delivery Address',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            TextField(
              controller: _addressController,
              maxLines: 3,
              decoration: const InputDecoration(
                hintText: 'Enter your complete delivery address',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTimeSlotSelection() {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.schedule, color: AppColors.primary),
                const SizedBox(width: 8),
                const Text(
                  'Delivery Time',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: RadioListTile<String>(
                    title: const Text('Morning\n(9 AM - 12 PM)'),
                    value: 'morning',
                    groupValue: _selectedTimeSlot,
                    onChanged: (value) {
                      setState(() {
                        _selectedTimeSlot = value!;
                      });
                    },
                  ),
                ),
                Expanded(
                  child: RadioListTile<String>(
                    title: const Text('Evening\n(4 PM - 7 PM)'),
                    value: 'evening',
                    groupValue: _selectedTimeSlot,
                    onChanged: (value) {
                      setState(() {
                        _selectedTimeSlot = value!;
                      });
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentMethod() {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.payment, color: AppColors.primary),
                const SizedBox(width: 8),
                const Text(
                  'Payment Method',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            RadioListTile<String>(
              title: const Text('Cash on Delivery'),
              subtitle: const Text('Pay when your order arrives'),
              value: 'cod',
              groupValue: _selectedPaymentMethod,
              onChanged: (value) {
                setState(() {
                  _selectedPaymentMethod = value!;
                });
              },
            ),
            RadioListTile<String>(
              title: const Text('Online Payment'),
              subtitle: const Text('Pay now via Razorpay'),
              value: 'online',
              groupValue: _selectedPaymentMethod,
              onChanged: (value) {
                setState(() {
                  _selectedPaymentMethod = value!;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderNotes() {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.note, color: AppColors.primary),
                const SizedBox(width: 8),
                const Text(
                  'Order Notes (Optional)',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            TextField(
              controller: _notesController,
              maxLines: 2,
              decoration: const InputDecoration(
                hintText: 'Any special instructions for delivery...',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPriceBreakdown() {
    final subtotal = widget.total;
    final deliveryCharges = subtotal >= 2000 ? 0.0 : 50.0;
    final gst = subtotal * 0.18;
    final total = subtotal + deliveryCharges + gst;

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            _buildPriceRow('Subtotal', subtotal),
            _buildPriceRow('GST (18%)', gst),
            _buildPriceRow('Delivery Charges', deliveryCharges),
            if (deliveryCharges == 0)
              Padding(
                padding: const EdgeInsets.only(top: 4),
                child: Text(
                  'Free delivery on orders above ₹2000',
                  style: TextStyle(
                    fontSize: 12,
                    color: AppColors.success,
                  ),
                ),
              ),
            const Divider(),
            _buildPriceRow('Total', total, isTotal: true),
          ],
        ),
      ),
    );
  }

  Widget _buildPriceRow(String label, double amount, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            '₹${amount.toStringAsFixed(0)}',
            style: TextStyle(
              fontSize: isTotal ? 16 : 14,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: isTotal ? AppColors.primary : null,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomBar() {
    final subtotal = widget.total;
    final deliveryCharges = subtotal >= 2000 ? 0.0 : 50.0;
    final gst = subtotal * 0.18;
    final total = subtotal + deliveryCharges + gst;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.3),
            blurRadius: 5,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: CustomButton(
          text: _isPlacingOrder 
              ? 'Placing Order...' 
              : 'Place Order - ₹${total.toStringAsFixed(0)}',
          onPressed: _isPlacingOrder ? null : _placeOrder,
          isLoading: _isPlacingOrder,
        ),
      ),
    );
  }

  void _placeOrder() async {
    if (_addressController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter delivery address')),
      );
      return;
    }

    setState(() {
      _isPlacingOrder = true;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final user = authProvider.currentUser;

      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Convert cart to CartItemModel list
      final cartItems = widget.cart.entries.map((entry) {
        final material = widget.materials.firstWhere(
          (m) => m['id'] == entry.key,
          orElse: () => {'name': 'Unknown', 'price': 0, 'unit': 'piece'},
        );
        
        return CartItemModel(
          materialId: entry.key,
          name: material['name'],
          price: material['price'].toDouble(),
          quantity: entry.value,
          unit: material['unit'],
        );
      }).toList();

      // Calculate total
      final subtotal = widget.total;
      final deliveryCharges = subtotal >= 2000 ? 0.0 : 50.0;
      final gst = subtotal * 0.18;
      final total = subtotal + deliveryCharges + gst;

      // Place order through real-time service
      final orderId = await _orderService.placeOrder(
        customerId: user.uid,
        vendorId: widget.vendor.id,
        vendorName: widget.vendor.businessName,
        items: cartItems,
        deliveryAddress: _addressController.text.trim(),
        location: {'lat': 28.4595, 'lng': 77.0266}, // Demo location
        paymentMethod: _selectedPaymentMethod,
        totalAmount: total,
      );

      // Clear cart
      final orderProvider = Provider.of<OrderProvider>(context, listen: false);
      orderProvider.clearCart();

      // Show success and navigate to tracking
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Order placed successfully! Order ID: ${orderId.substring(orderId.length - 6)}'),
          backgroundColor: AppColors.success,
        ),
      );

      // Navigate to order tracking
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (context) => OrderTrackingScreen(
            orderId: orderId,
            vendor: widget.vendor,
            cart: widget.cart,
            materials: widget.materials,
            deliveryAddress: _addressController.text.trim(),
            timeSlot: _selectedTimeSlot,
            total: total,
          ),
        ),
      );

    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to place order: $e'),
          backgroundColor: AppColors.error,
        ),
      );
    } finally {
      setState(() {
        _isPlacingOrder = false;
      });
    }
  }
}
