// import 'package:cloud_firestore/cloud_firestore.dart';

class InventoryModel {
  final String id;
  final String vendorId;
  final String vendorName;
  final String materialType;
  final String description;
  final double pricePerUnit;
  final int quantity;
  final String unit;
  final bool inStock;
  final List<String> photoUrls;
  final String status; // active, inactive
  final double vendorRating;
  final int minOrderQuantity;
  final Map<String, dynamic>? specifications;
  final DateTime createdAt;
  final DateTime updatedAt;

  InventoryModel({
    required this.id,
    required this.vendorId,
    required this.vendorName,
    required this.materialType,
    required this.description,
    required this.pricePerUnit,
    required this.quantity,
    required this.unit,
    required this.inStock,
    this.photoUrls = const [],
    this.status = 'active',
    this.vendorRating = 0.0,
    this.minOrderQuantity = 1,
    this.specifications,
    required this.createdAt,
    required this.updatedAt,
  });

  // factory InventoryModel.fromFirestore(DocumentSnapshot doc) {
  //   final data = doc.data() as Map<String, dynamic>;
  //   return InventoryModel(
  //     id: doc.id,
  //     vendorId: data['vendor_id'] ?? '',
  //     vendorName: data['vendor_name'] ?? '',
  //     materialType: data['material_type'] ?? '',
  //     description: data['description'] ?? '',
  //     pricePerUnit: (data['price_per_unit'] ?? 0.0).toDouble(),
  //     quantity: data['quantity'] ?? 0,
  //     unit: data['unit'] ?? '',
  //     inStock: data['in_stock'] ?? false,
  //     photoUrls: data['photo_urls'] != null
  //         ? List<String>.from(data['photo_urls'])
  //         : [],
  //     status: data['status'] ?? 'active',
  //     vendorRating: (data['vendor_rating'] ?? 0.0).toDouble(),
  //     minOrderQuantity: data['min_order_quantity'] ?? 1,
  //     specifications: data['specifications'],
  //     createdAt: (data['created_at'] as Timestamp?)?.toDate() ?? DateTime.now(),
  //     updatedAt: (data['updated_at'] as Timestamp?)?.toDate() ?? DateTime.now(),
  //   );
  // }

  factory InventoryModel.fromMap(Map<String, dynamic> data, String id) {
    return InventoryModel(
      id: id,
      vendorId: data['vendor_id'] ?? '',
      vendorName: data['vendor_name'] ?? '',
      materialType: data['material_type'] ?? '',
      description: data['description'] ?? '',
      pricePerUnit: (data['price_per_unit'] ?? 0.0).toDouble(),
      quantity: data['quantity'] ?? 0,
      unit: data['unit'] ?? '',
      inStock: data['in_stock'] ?? false,
      photoUrls: data['photo_urls'] != null 
          ? List<String>.from(data['photo_urls']) 
          : [],
      status: data['status'] ?? 'active',
      vendorRating: (data['vendor_rating'] ?? 0.0).toDouble(),
      minOrderQuantity: data['min_order_quantity'] ?? 1,
      specifications: data['specifications'],
      createdAt: data['created_at'] is String
          ? DateTime.parse(data['created_at'])
          : DateTime.now(),
      updatedAt: data['updated_at'] is String
          ? DateTime.parse(data['updated_at'])
          : DateTime.now(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'vendor_id': vendorId,
      'vendor_name': vendorName,
      'material_type': materialType,
      'description': description,
      'price_per_unit': pricePerUnit,
      'quantity': quantity,
      'unit': unit,
      'in_stock': inStock,
      'photo_urls': photoUrls,
      'status': status,
      'vendor_rating': vendorRating,
      'min_order_quantity': minOrderQuantity,
      'specifications': specifications,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  InventoryModel copyWith({
    String? vendorId,
    String? vendorName,
    String? materialType,
    String? description,
    double? pricePerUnit,
    int? quantity,
    String? unit,
    bool? inStock,
    List<String>? photoUrls,
    String? status,
    double? vendorRating,
    int? minOrderQuantity,
    Map<String, dynamic>? specifications,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return InventoryModel(
      id: id,
      vendorId: vendorId ?? this.vendorId,
      vendorName: vendorName ?? this.vendorName,
      materialType: materialType ?? this.materialType,
      description: description ?? this.description,
      pricePerUnit: pricePerUnit ?? this.pricePerUnit,
      quantity: quantity ?? this.quantity,
      unit: unit ?? this.unit,
      inStock: inStock ?? this.inStock,
      photoUrls: photoUrls ?? this.photoUrls,
      status: status ?? this.status,
      vendorRating: vendorRating ?? this.vendorRating,
      minOrderQuantity: minOrderQuantity ?? this.minOrderQuantity,
      specifications: specifications ?? this.specifications,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  String get priceDisplay {
    return '₹${pricePerUnit.toStringAsFixed(0)}/$unit';
  }

  String get quantityDisplay {
    return '$quantity $unit';
  }

  String get stockStatus {
    if (!inStock || quantity == 0) {
      return 'Out of Stock';
    } else if (quantity <= 10) {
      return 'Low Stock';
    } else {
      return 'In Stock';
    }
  }

  bool get isLowStock => quantity <= 10 && quantity > 0;
  bool get isOutOfStock => !inStock || quantity == 0;

  String get materialTypeDisplay {
    return materialType.split('_').map((word) => 
        word[0].toUpperCase() + word.substring(1)).join(' ');
  }

  String get vendorRatingDisplay {
    return '${vendorRating.toStringAsFixed(1)} ⭐';
  }

  double get totalValue => pricePerUnit * quantity;

  String get totalValueDisplay {
    return '₹${totalValue.toStringAsFixed(0)}';
  }

  bool get hasPhotos => photoUrls.isNotEmpty;

  String get primaryPhotoUrl {
    return photoUrls.isNotEmpty ? photoUrls.first : '';
  }

  Map<String, dynamic> get specificationsMap {
    return specifications ?? {};
  }

  bool get isActive => status == 'active';

  String get statusDisplay {
    switch (status) {
      case 'active':
        return 'Active';
      case 'inactive':
        return 'Inactive';
      default:
        return status.toUpperCase();
    }
  }

  bool canFulfillOrder(int requestedQuantity) {
    return inStock && 
           quantity >= requestedQuantity && 
           requestedQuantity >= minOrderQuantity &&
           status == 'active';
  }

  String getAvailabilityMessage(int requestedQuantity) {
    if (!inStock || quantity == 0) {
      return 'Out of stock';
    }
    if (requestedQuantity < minOrderQuantity) {
      return 'Minimum order: $minOrderQuantity $unit';
    }
    if (quantity < requestedQuantity) {
      return 'Only $quantity $unit available';
    }
    if (status != 'active') {
      return 'Currently unavailable';
    }
    return 'Available';
  }
}
