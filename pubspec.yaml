name: buildbid
description: A Flutter app for construction material bidding and ordering
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter

  # Firebase Backend - Production Ready for 10M Users
  firebase_core: ^2.24.2
  cloud_firestore: ^4.13.6
  firebase_auth: ^4.15.3
  firebase_messaging: ^14.7.10
  firebase_storage: ^11.5.6
  firebase_analytics: ^10.7.4
  firebase_crashlytics: ^3.4.9
  firebase_performance: ^0.9.3+8
  firebase_remote_config: ^4.3.8
  firebase_dynamic_links: ^5.4.8

  # State Management
  provider: ^6.1.1

  # UI Components & Animations
  cupertino_icons: ^1.0.6
  lottie: ^2.7.0
  country_code_picker: ^3.0.0
  sms_autofill: ^2.3.0
  cached_network_image: ^3.3.0
  photo_view: ^0.14.0
  carousel_slider: ^4.2.1
  shimmer: ^3.0.0
  flutter_staggered_grid_view: ^0.7.0

  # Charts & Data Visualization
  fl_chart: ^0.66.0

  # Utilities
  shared_preferences: ^2.2.2
  intl: ^0.20.2
  http: ^1.1.2
  image_picker: ^1.0.4
  permission_handler: ^11.0.1
  geolocator: ^10.1.0
  url_launcher: ^6.2.1
  path_provider: ^2.1.1
  device_info_plus: ^9.1.1
  connectivity_plus: ^5.0.2
  package_info_plus: ^4.2.0

  # Payments & E-commerce
  razorpay_flutter: ^1.3.6

  # Performance & Monitoring
  sentry_flutter: ^7.18.0

  # Advanced Networking
  dio: ^5.4.0
  dio_cache_interceptor: ^3.4.4

  # Security
  crypto: ^3.0.3
  encrypt: ^5.0.1

  # Background Processing
  workmanager: ^0.5.2

  # Push Notifications
  flutter_local_notifications: ^16.3.0

  # Real-time Features
  socket_io_client: ^2.0.3+1

  # Advanced UI
  flutter_svg: ^2.0.9
  animations: ^2.0.8
  rive: ^0.12.4

  # Localization
  flutter_localizations:
    sdk: flutter

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

flutter:
  uses-material-design: true
