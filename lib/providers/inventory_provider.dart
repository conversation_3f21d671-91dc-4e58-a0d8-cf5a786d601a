import 'package:flutter/material.dart';
// import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/inventory_model.dart';
import '../services/sample_data_service.dart';

class InventoryProvider extends ChangeNotifier {
  // final FirebaseFirestore _firestore = FirebaseService.firestore;
  
  List<InventoryModel> _inventory = [];
  List<InventoryModel> _filteredInventory = [];
  bool _isLoading = false;
  String? _errorMessage;
  String _searchQuery = '';
  String _selectedCategory = 'All';
  String _sortBy = 'name';
  bool _sortAscending = true;

  // Getters
  List<InventoryModel> get inventory => _filteredInventory;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  String get searchQuery => _searchQuery;
  String get selectedCategory => _selectedCategory;
  String get sortBy => _sortBy;
  bool get sortAscending => _sortAscending;

  // Initialize with sample data
  InventoryProvider() {
    loadInventory();
  }

  // Load all inventory
  Future<void> loadInventory() async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      await Future.delayed(const Duration(milliseconds: 500)); // Simulate network delay
      _inventory = SampleDataService.getInventoryItems();

      _applyFilters();
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _errorMessage = e.toString();
      _isLoading = false;
      notifyListeners();
    }
  }

  // Load inventory for a specific vendor
  Future<void> loadVendorInventory(String vendorId) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      await Future.delayed(const Duration(milliseconds: 300));
      final allInventory = SampleDataService.getInventoryItems();
      _inventory = allInventory.where((item) => item.vendorId == vendorId).toList();

      _applyFilters();
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _errorMessage = e.toString();
      _isLoading = false;
      notifyListeners();
    }
  }

  // Load inventory by category for mistri/buyer view
  Future<void> loadInventoryByCategory(String category, {String? location}) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      await Future.delayed(const Duration(milliseconds: 300));
      final allInventory = SampleDataService.getInventoryItems();

      _inventory = allInventory.where((item) {
        bool categoryMatch = category == 'All' ||
                           item.materialType.toLowerCase() == category.toLowerCase();
        bool stockMatch = item.inStock && item.quantity > 0;
        bool statusMatch = item.status == 'active';

        return categoryMatch && stockMatch && statusMatch;
      }).toList();

      _selectedCategory = category;
      _applyFilters();
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _errorMessage = e.toString();
      _isLoading = false;
      notifyListeners();
    }
  }

  // Add new inventory item
  Future<bool> addInventoryItem(InventoryModel item) async {
    try {
      _isLoading = true;
      notifyListeners();

      await Future.delayed(const Duration(milliseconds: 300));

      // Generate new ID
      final newItem = item.copyWith(
        // id: 'inv_${DateTime.now().millisecondsSinceEpoch}',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      _inventory.add(newItem);
      _applyFilters();

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _errorMessage = e.toString();
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  // Update inventory item
  Future<bool> updateInventoryItem(InventoryModel item) async {
    try {
      _isLoading = true;
      notifyListeners();

      await Future.delayed(const Duration(milliseconds: 300));

      // Update local list
      final index = _inventory.indexWhere((i) => i.id == item.id);
      if (index != -1) {
        _inventory[index] = item.copyWith(updatedAt: DateTime.now());
        _applyFilters();
      }

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _errorMessage = e.toString();
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  // Delete inventory item
  Future<bool> deleteInventoryItem(String itemId) async {
    try {
      _isLoading = true;
      notifyListeners();

      await Future.delayed(const Duration(milliseconds: 300));

      // Remove from local list
      _inventory.removeWhere((item) => item.id == itemId);
      _applyFilters();

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _errorMessage = e.toString();
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  // Update stock quantity
  Future<bool> updateStock(String itemId, int newQuantity) async {
    try {
      await Future.delayed(const Duration(milliseconds: 200));

      // Update local list
      final index = _inventory.indexWhere((item) => item.id == itemId);
      if (index != -1) {
        _inventory[index] = _inventory[index].copyWith(
          quantity: newQuantity,
          inStock: newQuantity > 0,
          updatedAt: DateTime.now(),
        );
        _applyFilters();
        notifyListeners();
      }

      return true;
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Search inventory
  void searchInventory(String query) {
    _searchQuery = query;
    _applyFilters();
    notifyListeners();
  }

  // Filter by category
  void filterByCategory(String category) {
    _selectedCategory = category;
    _applyFilters();
    notifyListeners();
  }

  // Sort inventory
  void sortInventory(String sortBy, {bool? ascending}) {
    _sortBy = sortBy;
    if (ascending != null) {
      _sortAscending = ascending;
    } else {
      _sortAscending = !_sortAscending;
    }
    _applyFilters();
    notifyListeners();
  }

  // Apply filters and sorting
  void _applyFilters() {
    _filteredInventory = List.from(_inventory);

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      _filteredInventory = _filteredInventory.where((item) {
        return item.materialType.toLowerCase().contains(_searchQuery.toLowerCase()) ||
               item.description.toLowerCase().contains(_searchQuery.toLowerCase()) ||
               item.vendorName.toLowerCase().contains(_searchQuery.toLowerCase());
      }).toList();
    }

    // Apply category filter
    if (_selectedCategory != 'All') {
      _filteredInventory = _filteredInventory.where((item) {
        return item.materialType.toLowerCase() == _selectedCategory.toLowerCase();
      }).toList();
    }

    // Apply sorting
    _filteredInventory.sort((a, b) {
      int comparison = 0;
      switch (_sortBy) {
        case 'name':
          comparison = a.materialType.compareTo(b.materialType);
          break;
        case 'price':
          comparison = a.pricePerUnit.compareTo(b.pricePerUnit);
          break;
        case 'quantity':
          comparison = a.quantity.compareTo(b.quantity);
          break;
        case 'rating':
          comparison = a.vendorRating.compareTo(b.vendorRating);
          break;
        case 'date':
          comparison = a.createdAt.compareTo(b.createdAt);
          break;
        default:
          comparison = a.materialType.compareTo(b.materialType);
      }
      return _sortAscending ? comparison : -comparison;
    });
  }

  // Get inventory item by ID
  InventoryModel? getInventoryItem(String itemId) {
    try {
      return _inventory.firstWhere((item) => item.id == itemId);
    } catch (e) {
      return null;
    }
  }

  // Get available categories
  List<String> getAvailableCategories() {
    final categories = _inventory
        .map((item) => item.materialType)
        .toSet()
        .toList();
    categories.sort();
    return ['All', ...categories];
  }

  // Clear filters
  void clearFilters() {
    _searchQuery = '';
    _selectedCategory = 'All';
    _sortBy = 'name';
    _sortAscending = true;
    _applyFilters();
    notifyListeners();
  }

  // Clear error
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Get low stock items (for vendor dashboard)
  List<InventoryModel> getLowStockItems({int threshold = 10}) {
    return _inventory.where((item) => item.quantity <= threshold).toList();
  }

  // Get out of stock items
  List<InventoryModel> getOutOfStockItems() {
    return _inventory.where((item) => !item.inStock || item.quantity == 0).toList();
  }

  // Get total inventory value for vendor
  double getTotalInventoryValue() {
    return _inventory.fold(0.0, (total, item) => total + (item.pricePerUnit * item.quantity));
  }

  // Get inventory statistics
  Map<String, dynamic> getInventoryStats() {
    final totalItems = _inventory.length;
    final inStockItems = _inventory.where((item) => item.inStock).length;
    final outOfStockItems = totalItems - inStockItems;
    final totalValue = getTotalInventoryValue();
    final lowStockItems = getLowStockItems().length;

    return {
      'totalItems': totalItems,
      'inStockItems': inStockItems,
      'outOfStockItems': outOfStockItems,
      'lowStockItems': lowStockItems,
      'totalValue': totalValue,
    };
  }
}
