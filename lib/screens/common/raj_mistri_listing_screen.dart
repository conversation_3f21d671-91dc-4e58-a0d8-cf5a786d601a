import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/service_provider.dart';
import '../../providers/location_provider.dart';
import '../../utils/app_colors.dart';
import '../../widgets/custom_search_field.dart';
import '../../models/service_model.dart';

class RajMistriListingScreen extends StatefulWidget {
  final String? category;

  const RajMistriListingScreen({
    Key? key,
    this.category,
  }) : super(key: key);

  @override
  State<RajMistriListingScreen> createState() => _RajMistriListingScreenState();
}

class _RajMistriListingScreenState extends State<RajMistriListingScreen>
    with TickerProviderStateMixin {
  final _searchController = TextEditingController();
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  bool _showFilters = false;
  String _sortBy = 'rating'; // rating, price, experience, distance

  final List<String> _serviceCategories = [
    'All', 'Masonry', 'Plumbing', 'Electrical', 'Painting', 'Tiling', 'Carpentry'
  ];

  final List<String> _experienceRanges = [
    'All', '1-3 years', '3-5 years', '5-10 years', '10+ years'
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadServices();
      if (widget.category != null) {
        context.read<ServiceProvider>().setCategoryFilter(widget.category!);
      }
    });

    _animationController.forward();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadServices() async {
    final serviceProvider = context.read<ServiceProvider>();
    final locationProvider = context.read<LocationProvider>();
    
    await serviceProvider.loadServices();
    
    if (locationProvider.currentLocation != null) {
      await serviceProvider.loadNearbyServices(locationProvider.currentLocation!);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.category != null ? '${widget.category} Services' : 'Raj Mistri Services'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(_showFilters ? Icons.filter_list : Icons.filter_list_outlined),
            onPressed: () {
              setState(() {
                _showFilters = !_showFilters;
              });
            },
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.sort),
            onSelected: (value) {
              setState(() {
                _sortBy = value;
              });
              _sortServices(value);
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'rating',
                child: Row(
                  children: [
                    Icon(Icons.star),
                    SizedBox(width: 8),
                    Text('Rating'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'price',
                child: Row(
                  children: [
                    Icon(Icons.currency_rupee),
                    SizedBox(width: 8),
                    Text('Price'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'experience',
                child: Row(
                  children: [
                    Icon(Icons.work),
                    SizedBox(width: 8),
                    Text('Experience'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'distance',
                child: Row(
                  children: [
                    Icon(Icons.location_on),
                    SizedBox(width: 8),
                    Text('Distance'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Column(
          children: [
            // Search Section
            Container(
              color: AppColors.primary,
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              child: CustomSearchField(
                controller: _searchController,
                hint: 'Search services, providers...',
                onChanged: (value) {
                  context.read<ServiceProvider>().searchServices(value);
                },
              ),
            ),

            // Filters Section
            if (_showFilters) _buildFiltersSection(),

            // Quick Filter Chips
            Container(
              height: 50,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: ListView(
                scrollDirection: Axis.horizontal,
                children: [
                  _buildFilterChip('Available Today', context.read<ServiceProvider>().availableToday, () {
                    context.read<ServiceProvider>().setAvailableTodayFilter(
                        !context.read<ServiceProvider>().availableToday);
                  }),
                  const SizedBox(width: 8),
                  _buildFilterChip('Verified', false, () {
                    // TODO: Implement verified filter
                  }),
                  const SizedBox(width: 8),
                  _buildFilterChip('Top Rated', false, () {
                    context.read<ServiceProvider>().setRatingFilter(4.5);
                  }),
                  const SizedBox(width: 8),
                  _buildFilterChip('Experienced', false, () {
                    context.read<ServiceProvider>().setExperienceFilter(5);
                  }),
                ],
              ),
            ),

            const Divider(height: 1),

            // Services List
            Expanded(
              child: Consumer<ServiceProvider>(
                builder: (context, serviceProvider, child) {
                  if (serviceProvider.isLoading) {
                    return _buildLoadingState();
                  }

                  if (serviceProvider.errorMessage != null) {
                    return _buildErrorState(serviceProvider.errorMessage!);
                  }

                  final services = serviceProvider.filteredServices;

                  if (services.isEmpty) {
                    return _buildEmptyState();
                  }

                  return RefreshIndicator(
                    onRefresh: _loadServices,
                    child: ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: services.length,
                      itemBuilder: (context, index) {
                        final service = services[index];
                        return Padding(
                          padding: const EdgeInsets.only(bottom: 16),
                          child: _buildServiceCard(service),
                        );
                      },
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFiltersSection() {
    return Container(
      color: Colors.grey.shade50,
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Filters',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          // Service Category Filter
          const Text('Service Category', style: TextStyle(fontWeight: FontWeight.w600)),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            children: _serviceCategories.map((category) {
              final isSelected = context.watch<ServiceProvider>().selectedCategory == category ||
                  (category == 'All' && context.watch<ServiceProvider>().selectedCategory.isEmpty);
              return FilterChip(
                label: Text(category),
                selected: isSelected,
                onSelected: (selected) {
                  context.read<ServiceProvider>().setCategoryFilter(
                      selected ? (category == 'All' ? '' : category) : '');
                },
                selectedColor: AppColors.primary.withOpacity(0.2),
                checkmarkColor: AppColors.primary,
              );
            }).toList(),
          ),
          
          const SizedBox(height: 16),
          
          // Rating Filter
          const Text('Minimum Rating', style: TextStyle(fontWeight: FontWeight.w600)),
          const SizedBox(height: 8),
          Consumer<ServiceProvider>(
            builder: (context, serviceProvider, child) {
              return Slider(
                value: serviceProvider.minRating,
                min: 0.0,
                max: 5.0,
                divisions: 10,
                label: '${serviceProvider.minRating.toStringAsFixed(1)} ⭐',
                onChanged: (value) {
                  serviceProvider.setRatingFilter(value);
                },
                activeColor: AppColors.primary,
              );
            },
          ),
          
          const SizedBox(height: 16),
          
          // Price Range Filter
          const Text('Price Range (per hour)', style: TextStyle(fontWeight: FontWeight.w600)),
          const SizedBox(height: 8),
          Consumer<ServiceProvider>(
            builder: (context, serviceProvider, child) {
              return RangeSlider(
                values: RangeValues(serviceProvider.minPrice, serviceProvider.maxPrice),
                min: 0,
                max: 1000,
                divisions: 20,
                labels: RangeLabels(
                  '₹${serviceProvider.minPrice.round()}',
                  '₹${serviceProvider.maxPrice.round()}',
                ),
                onChanged: (values) {
                  serviceProvider.setPriceRangeFilter(values.start, values.end);
                },
                activeColor: AppColors.primary,
              );
            },
          ),
          
          const SizedBox(height: 16),
          
          // Clear Filters Button
          SizedBox(
            width: double.infinity,
            child: OutlinedButton(
              onPressed: () {
                context.read<ServiceProvider>().clearFilters();
              },
              child: const Text('Clear All Filters'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, bool isSelected, VoidCallback onTap) {
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (_) => onTap(),
      selectedColor: AppColors.primary.withOpacity(0.2),
      checkmarkColor: AppColors.primary,
    );
  }

  Widget _buildServiceCard(ServiceModel service) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () {
          // TODO: Navigate to service detail screen
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  // Provider Image
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color: AppColors.greyLight,
                      borderRadius: BorderRadius.circular(30),
                      image: service.providerImage != null
                          ? DecorationImage(
                              image: NetworkImage(service.providerImage!),
                              fit: BoxFit.cover,
                            )
                          : null,
                    ),
                    child: service.providerImage == null
                        ? Icon(
                            Icons.person,
                            color: AppColors.textSecondary,
                            size: 24,
                          )
                        : null,
                  ),
                  const SizedBox(width: 16),

                  // Provider Info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                service.providerName,
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            if (service.isVerified)
                              Container(
                                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                decoration: BoxDecoration(
                                  color: AppColors.success.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      Icons.verified,
                                      size: 12,
                                      color: AppColors.success,
                                    ),
                                    const SizedBox(width: 2),
                                    Text(
                                      'Verified',
                                      style: TextStyle(
                                        fontSize: 10,
                                        fontWeight: FontWeight.w600,
                                        color: AppColors.success,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Text(
                          service.category.toUpperCase(),
                          style: TextStyle(
                            fontSize: 14,
                            color: AppColors.textSecondary,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Icon(
                              Icons.star,
                              size: 16,
                              color: AppColors.warning,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              service.rating.toString(),
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            Text(
                              ' (${service.totalJobs} jobs)',
                              style: TextStyle(
                                fontSize: 12,
                                color: AppColors.textSecondary,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Icon(
                              Icons.work,
                              size: 16,
                              color: AppColors.textSecondary,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '${service.yearsExperience} years',
                              style: TextStyle(
                                fontSize: 12,
                                color: AppColors.textSecondary,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  // Availability Status
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: service.isAvailable
                          ? AppColors.success.withOpacity(0.1)
                          : AppColors.error.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      service.isAvailable ? 'Available' : 'Busy',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: service.isAvailable ? AppColors.success : AppColors.error,
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Skills
              if (service.skills.isNotEmpty) ...[
                Wrap(
                  spacing: 6,
                  runSpacing: 6,
                  children: service.skills.take(4).map((skill) {
                    return Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: AppColors.primary.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        skill,
                        style: TextStyle(
                          fontSize: 11,
                          fontWeight: FontWeight.w500,
                          color: AppColors.primary,
                        ),
                      ),
                    );
                  }).toList(),
                ),
                const SizedBox(height: 12),
              ],

              // Pricing and Location
              Row(
                children: [
                  Text(
                    '₹${service.pricePerHour.toStringAsFixed(0)}/hr',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '• ₹${service.pricePerDay.toStringAsFixed(0)}/day',
                    style: TextStyle(
                      fontSize: 14,
                      color: AppColors.textSecondary,
                    ),
                  ),
                  const Spacer(),
                  Icon(
                    Icons.location_on,
                    size: 16,
                    color: AppColors.textSecondary,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    service.city,
                    style: TextStyle(
                      fontSize: 12,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: 5,
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: Container(
            height: 140,
            decoration: BoxDecoration(
              color: AppColors.greyLight,
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        );
      },
    );
  }

  Widget _buildErrorState(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: AppColors.error,
          ),
          const SizedBox(height: 16),
          Text(
            'Error loading services',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: TextStyle(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _loadServices,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.engineering_outlined,
            size: 64,
            color: AppColors.textSecondary,
          ),
          const SizedBox(height: 16),
          Text(
            'No services found',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Try adjusting your filters or search terms',
            style: TextStyle(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              context.read<ServiceProvider>().clearFilters();
            },
            child: const Text('Clear Filters'),
          ),
        ],
      ),
    );
  }

  void _sortServices(String sortBy) {
    final serviceProvider = context.read<ServiceProvider>();
    List<ServiceModel> services = List.from(serviceProvider.filteredServices);

    switch (sortBy) {
      case 'rating':
        services.sort((a, b) => b.rating.compareTo(a.rating));
        break;
      case 'price':
        services.sort((a, b) => a.pricePerHour.compareTo(b.pricePerHour));
        break;
      case 'experience':
        services.sort((a, b) => b.yearsExperience.compareTo(a.yearsExperience));
        break;
      case 'distance':
        // TODO: Implement distance sorting
        break;
    }

    // Update the filtered services list
    serviceProvider.filteredServices.clear();
    serviceProvider.filteredServices.addAll(services);
    serviceProvider.notifyListeners();
  }
}
