const express = require('express');
const { body, query, validationResult } = require('express-validator');
const Material = require('../models/Material');
const Vendor = require('../models/Vendor');
const { authenticateToken, authorize } = require('../middleware/auth');
const router = express.Router();

/**
 * @swagger
 * /api/materials:
 *   get:
 *     summary: Get all materials with filters and search
 *     tags: [Materials]
 *     parameters:
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search query for materials
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *         description: Filter by category
 *       - in: query
 *         name: vendorId
 *         schema:
 *           type: string
 *         description: Filter by vendor ID
 *       - in: query
 *         name: minPrice
 *         schema:
 *           type: number
 *         description: Minimum price filter
 *       - in: query
 *         name: maxPrice
 *         schema:
 *           type: number
 *         description: Maximum price filter
 *       - in: query
 *         name: inStock
 *         schema:
 *           type: boolean
 *         description: Filter by stock availability
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number (default: 1)
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Items per page (default: 20)
 *       - in: query
 *         name: sort
 *         schema:
 *           type: string
 *           enum: [price_asc, price_desc, rating_desc, newest, oldest]
 *         description: Sort order
 *     responses:
 *       200:
 *         description: Materials retrieved successfully
 */
router.get('/', [
  query('search').optional().isString(),
  query('category').optional().isString(),
  query('vendorId').optional().isMongoId(),
  query('minPrice').optional().isFloat({ min: 0 }),
  query('maxPrice').optional().isFloat({ min: 0 }),
  query('inStock').optional().isBoolean(),
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('sort').optional().isIn(['price_asc', 'price_desc', 'rating_desc', 'newest', 'oldest'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const {
      search,
      category,
      vendorId,
      minPrice,
      maxPrice,
      inStock,
      page = 1,
      limit = 20,
      sort = 'newest'
    } = req.query;

    // Build query
    const query = {
      isActive: true,
      isApproved: true
    };

    if (category) {
      query.category = category;
    }

    if (vendorId) {
      query.vendorId = vendorId;
    }

    if (minPrice || maxPrice) {
      query['pricing.basePrice'] = {};
      if (minPrice) query['pricing.basePrice'].$gte = parseFloat(minPrice);
      if (maxPrice) query['pricing.basePrice'].$lte = parseFloat(maxPrice);
    }

    if (inStock === 'true') {
      query['inventory.currentStock'] = { $gt: 0 };
    }

    // Build sort
    let sortQuery = {};
    switch (sort) {
      case 'price_asc':
        sortQuery = { 'pricing.basePrice': 1 };
        break;
      case 'price_desc':
        sortQuery = { 'pricing.basePrice': -1 };
        break;
      case 'rating_desc':
        sortQuery = { 'metrics.rating.average': -1 };
        break;
      case 'oldest':
        sortQuery = { createdAt: 1 };
        break;
      default: // newest
        sortQuery = { createdAt: -1 };
    }

    // Demo data for when database is not available
    const demoMaterials = [
      {
        _id: '1',
        name: 'Red Clay Bricks',
        description: 'High quality red clay bricks for construction',
        category: 'bricks',
        pricing: { basePrice: 8, unit: 'piece' },
        inventory: { currentStock: 5000 },
        vendorId: {
          _id: '1',
          businessName: 'Kumar Construction Materials',
          rating: { average: 4.5 }
        },
        isActive: true,
        isApproved: true
      },
      {
        _id: '2',
        name: 'OPC Cement 53 Grade',
        description: 'Ordinary Portland Cement, 53 grade',
        category: 'cement',
        pricing: { basePrice: 370, unit: 'bag' },
        inventory: { currentStock: 200 },
        vendorId: {
          _id: '1',
          businessName: 'Kumar Construction Materials',
          rating: { average: 4.5 }
        },
        isActive: true,
        isApproved: true
      },
      {
        _id: '3',
        name: 'TMT Steel Bars 12mm',
        description: 'Thermo-Mechanically Treated steel bars',
        category: 'steel',
        pricing: { basePrice: 65, unit: 'kg' },
        inventory: { currentStock: 1000 },
        vendorId: {
          _id: '2',
          businessName: 'Sharma Steel & Cement',
          rating: { average: 4.8 }
        },
        isActive: true,
        isApproved: true
      }
    ];

    let materials = demoMaterials;
    let totalCount = demoMaterials.length;

    // Apply filters to demo data
    if (category) {
      materials = materials.filter(m => m.category === category);
    }
    if (vendorId) {
      materials = materials.filter(m => m.vendorId._id === vendorId);
    }
    if (search) {
      materials = materials.filter(m =>
        m.name.toLowerCase().includes(search.toLowerCase()) ||
        m.description.toLowerCase().includes(search.toLowerCase())
      );
    }

    res.json({
      success: true,
      data: {
        materials,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(totalCount / parseInt(limit)),
          totalItems: totalCount,
          itemsPerPage: parseInt(limit)
        }
      }
    });
  } catch (error) {
    console.error('Get materials error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get materials'
    });
  }
});

/**
 * @swagger
 * /api/materials/{id}:
 *   get:
 *     summary: Get material by ID
 *     tags: [Materials]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Material ID
 *     responses:
 *       200:
 *         description: Material retrieved successfully
 *       404:
 *         description: Material not found
 */
router.get('/:id', async (req, res) => {
  try {
    const material = await Material.findById(req.params.id)
      .populate('vendorId', 'businessName rating location businessHours');

    if (!material) {
      return res.status(404).json({
        success: false,
        message: 'Material not found'
      });
    }

    // Increment view count
    material.metrics.views += 1;
    await material.save();

    res.json({
      success: true,
      data: material
    });
  } catch (error) {
    console.error('Get material error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get material'
    });
  }
});

/**
 * @swagger
 * /api/materials:
 *   post:
 *     summary: Create new material
 *     tags: [Materials]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - description
 *               - category
 *               - pricing
 *               - inventory
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               category:
 *                 type: string
 *               pricing:
 *                 type: object
 *                 properties:
 *                   basePrice:
 *                     type: number
 *                   unit:
 *                     type: string
 *               inventory:
 *                 type: object
 *                 properties:
 *                   currentStock:
 *                     type: number
 *     responses:
 *       201:
 *         description: Material created successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 */
router.post('/', authenticateToken, [
  body('name')
    .trim()
    .isLength({ min: 2, max: 200 })
    .withMessage('Material name must be between 2 and 200 characters'),
  body('description')
    .trim()
    .isLength({ min: 10, max: 1000 })
    .withMessage('Description must be between 10 and 1000 characters'),
  body('category')
    .isIn([
      'bricks', 'cement', 'steel', 'sand', 'gravel', 'stone',
      'paints', 'tiles', 'marble', 'granite', 'hardware',
      'plumbing', 'electrical', 'roofing', 'flooring', 'other'
    ])
    .withMessage('Invalid category'),
  body('pricing.basePrice')
    .isFloat({ min: 0 })
    .withMessage('Base price must be a positive number'),
  body('pricing.unit')
    .isIn(['piece', 'bag', 'ton', 'cubic_meter', 'square_meter', 'liter', 'kg', 'meter', 'box', 'bundle'])
    .withMessage('Invalid unit'),
  body('inventory.currentStock')
    .isInt({ min: 0 })
    .withMessage('Current stock must be a non-negative integer')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    // Check if user has vendor profile
    const vendor = await Vendor.findOne({ userId: req.user.userId });
    if (!vendor) {
      return res.status(400).json({
        success: false,
        message: 'Vendor profile required to add materials'
      });
    }

    // Create material
    const material = new Material({
      vendorId: vendor._id,
      ...req.body
    });

    await material.save();

    res.status(201).json({
      success: true,
      message: 'Material created successfully',
      data: material
    });
  } catch (error) {
    console.error('Create material error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create material'
    });
  }
});

/**
 * @swagger
 * /api/materials/{id}:
 *   put:
 *     summary: Update material
 *     tags: [Materials]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Material ID
 *     responses:
 *       200:
 *         description: Material updated successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 *       404:
 *         description: Material not found
 */
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const material = await Material.findById(req.params.id).populate('vendorId');
    if (!material) {
      return res.status(404).json({
        success: false,
        message: 'Material not found'
      });
    }

    // Check if user owns this material
    if (material.vendorId.userId.toString() !== req.user.userId) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    // Update material
    const allowedFields = [
      'name', 'description', 'category', 'subcategory', 'brand', 'model',
      'specifications', 'pricing', 'inventory', 'delivery', 'tags',
      'searchKeywords', 'isActive'
    ];

    allowedFields.forEach(field => {
      if (req.body[field] !== undefined) {
        material[field] = req.body[field];
      }
    });

    await material.save();

    res.json({
      success: true,
      message: 'Material updated successfully',
      data: material
    });
  } catch (error) {
    console.error('Update material error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update material'
    });
  }
});

/**
 * @swagger
 * /api/materials/{id}:
 *   delete:
 *     summary: Delete material
 *     tags: [Materials]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Material ID
 *     responses:
 *       200:
 *         description: Material deleted successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 *       404:
 *         description: Material not found
 */
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const material = await Material.findById(req.params.id).populate('vendorId');
    if (!material) {
      return res.status(404).json({
        success: false,
        message: 'Material not found'
      });
    }

    // Check if user owns this material
    if (material.vendorId.userId.toString() !== req.user.userId) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    // Soft delete by setting isActive to false
    material.isActive = false;
    await material.save();

    res.json({
      success: true,
      message: 'Material deleted successfully'
    });
  } catch (error) {
    console.error('Delete material error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete material'
    });
  }
});

/**
 * @swagger
 * /api/materials/vendor/{vendorId}:
 *   get:
 *     summary: Get materials by vendor ID
 *     tags: [Materials]
 *     parameters:
 *       - in: path
 *         name: vendorId
 *         required: true
 *         schema:
 *           type: string
 *         description: Vendor ID
 *     responses:
 *       200:
 *         description: Vendor materials retrieved successfully
 */
router.get('/vendor/:vendorId', async (req, res) => {
  try {
    const materials = await Material.find({
      vendorId: req.params.vendorId,
      isActive: true,
      isApproved: true
    })
      .sort({ createdAt: -1 })
      .limit(50);

    res.json({
      success: true,
      data: materials
    });
  } catch (error) {
    console.error('Get vendor materials error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get vendor materials'
    });
  }
});

module.exports = router;
