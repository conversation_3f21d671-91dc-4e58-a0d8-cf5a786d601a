import 'dart:convert';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:http/http.dart' as http;

class LocationService {
  static final LocationService _instance = LocationService._internal();
  factory LocationService() => _instance;
  LocationService._internal();

  // Get current location with proper error handling
  Future<Position?> getCurrentLocation() async {
    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        throw Exception('Location services are disabled');
      }

      // Check location permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          throw Exception('Location permissions are denied');
        }
      }

      if (permission == LocationPermission.deniedForever) {
        throw Exception('Location permissions are permanently denied');
      }

      // Get current position with high accuracy
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );

      return position;
    } catch (e) {
      print('Error getting location: $e');
      return null;
    }
  }

  // Get address from coordinates using reverse geocoding
  Future<String?> getAddressFromCoordinates(double latitude, double longitude) async {
    try {
      // Using OpenStreetMap Nominatim API (free alternative to Google)
      final url = 'https://nominatim.openstreetmap.org/reverse?format=json&lat=$latitude&lon=$longitude&zoom=18&addressdetails=1';
      
      final response = await http.get(
        Uri.parse(url),
        headers: {
          'User-Agent': 'BuildBid/1.0.0', // Required by Nominatim
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        
        if (data['display_name'] != null) {
          // Extract meaningful address parts
          final address = data['address'];
          String formattedAddress = '';
          
          if (address != null) {
            // Build address from components
            List<String> addressParts = [];
            
            if (address['house_number'] != null) {
              addressParts.add(address['house_number']);
            }
            if (address['road'] != null) {
              addressParts.add(address['road']);
            }
            if (address['suburb'] != null) {
              addressParts.add(address['suburb']);
            }
            if (address['city'] != null) {
              addressParts.add(address['city']);
            } else if (address['town'] != null) {
              addressParts.add(address['town']);
            } else if (address['village'] != null) {
              addressParts.add(address['village']);
            }
            
            formattedAddress = addressParts.take(3).join(', ');
          }
          
          return formattedAddress.isNotEmpty ? formattedAddress : data['display_name'];
        }
      }
      
      return null;
    } catch (e) {
      print('Error getting address: $e');
      return null;
    }
  }

  // Get distance between two points
  double getDistanceBetween(
    double startLatitude,
    double startLongitude,
    double endLatitude,
    double endLongitude,
  ) {
    return Geolocator.distanceBetween(
      startLatitude,
      startLongitude,
      endLatitude,
      endLongitude,
    );
  }

  // Check if location permission is granted
  Future<bool> isLocationPermissionGranted() async {
    final permission = await Geolocator.checkPermission();
    return permission == LocationPermission.always || 
           permission == LocationPermission.whileInUse;
  }

  // Request location permission
  Future<bool> requestLocationPermission() async {
    final permission = await Geolocator.requestPermission();
    return permission == LocationPermission.always || 
           permission == LocationPermission.whileInUse;
  }

  // Get nearby vendors based on current location
  Future<List<Map<String, dynamic>>> getNearbyVendors(Position position, {double radiusKm = 10}) async {
    try {
      // This would typically call your backend API
      // For demo, returning mock data with calculated distances
      
      final mockVendors = [
        {
          'id': '1',
          'name': 'Kumar Construction Materials',
          'category': 'Bricks Specialist',
          'latitude': position.latitude + 0.01,
          'longitude': position.longitude + 0.01,
          'rating': 4.5,
          'reviews': 127,
          'price': '₹8/pc',
          'isOpen': true,
        },
        {
          'id': '2',
          'name': 'Sharma Steel & Cement',
          'category': 'Cement & Steel',
          'latitude': position.latitude - 0.005,
          'longitude': position.longitude + 0.008,
          'rating': 4.8,
          'reviews': 89,
          'price': '₹370/bag',
          'isOpen': true,
        },
        {
          'id': '3',
          'name': 'Singh Paints & Hardware',
          'category': 'Paints & Tools',
          'latitude': position.latitude + 0.015,
          'longitude': position.longitude - 0.01,
          'rating': 4.2,
          'reviews': 156,
          'price': '₹320/litre',
          'isOpen': false,
        },
      ];

      // Calculate distances and add to vendor data
      for (var vendor in mockVendors) {
        final distance = getDistanceBetween(
          position.latitude,
          position.longitude,
          vendor['latitude'] as double,
          vendor['longitude'] as double,
        );
        
        vendor['distance'] = distance;
        vendor['distanceText'] = distance < 1000 
          ? '${distance.round()} m away'
          : '${(distance / 1000).toStringAsFixed(1)} km away';
        
        // Estimate delivery time based on distance
        final deliveryMinutes = (distance / 1000 * 15 + 20).round(); // 15 min per km + 20 min base
        vendor['deliveryTime'] = '$deliveryMinutes-${deliveryMinutes + 15} mins';
      }

      // Sort by distance
      mockVendors.sort((a, b) => (a['distance'] as double).compareTo(b['distance'] as double));

      // Filter by radius
      return mockVendors.where((vendor) => (vendor['distance'] as double) <= radiusKm * 1000).toList();
      
    } catch (e) {
      print('Error getting nearby vendors: $e');
      return [];
    }
  }

  // Get current city for location-based services
  Future<String?> getCurrentCity() async {
    try {
      final position = await getCurrentLocation();
      if (position != null) {
        final address = await getAddressFromCoordinates(position.latitude, position.longitude);
        if (address != null) {
          // Extract city from address
          final parts = address.split(', ');
          return parts.length > 1 ? parts.last : address;
        }
      }
      return null;
    } catch (e) {
      print('Error getting current city: $e');
      return null;
    }
  }

  // Watch position changes for real-time tracking
  Stream<Position> watchPosition() {
    return Geolocator.getPositionStream(
      locationSettings: const LocationSettings(
        accuracy: LocationAccuracy.high,
        distanceFilter: 10, // Update every 10 meters
      ),
    );
  }

  // Calculate ETA for delivery
  String calculateDeliveryETA(double distanceKm) {
    // Base delivery time + travel time
    final baseTime = 20; // 20 minutes base preparation time
    final travelTime = (distanceKm * 10).round(); // 10 minutes per km
    final totalTime = baseTime + travelTime;
    
    return '$totalTime-${totalTime + 15} mins';
  }
}
