import 'dart:async';
import 'package:flutter/material.dart';
// import 'package:cloud_firestore/cloud_firestore.dart';
// import 'package:firebase_messaging/firebase_messaging.dart';
import '../models/notification_model.dart';
// import '../services/firebase_service.dart';

class NotificationProvider extends ChangeNotifier {
  // final FirebaseFirestore _firestore = FirebaseService.firestore;
  // final FirebaseMessaging _messaging = FirebaseService.messaging;

  List<NotificationModel> _notifications = [];
  bool _isLoading = false;
  String? _errorMessage;
  int _unreadCount = 0;
  // StreamSubscription<QuerySnapshot>? _notificationSubscription;

  // Getters
  List<NotificationModel> get notifications => _notifications;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  int get unreadCount => _unreadCount;
  bool get hasUnreadNotifications => _unreadCount > 0;

  // Initialize notifications for a user
  Future<void> initializeNotifications(String userId) async {
    try {
      // Load sample notifications
      await loadNotifications(userId);
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
    }
  }

  // Load notifications for a user
  Future<void> loadNotifications(String userId) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      await Future.delayed(const Duration(milliseconds: 500)); // Simulate network delay

      // Create sample notifications
      _notifications = _createSampleNotifications(userId);

      _updateUnreadCount();
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _errorMessage = e.toString();
      _isLoading = false;
      notifyListeners();
    }
  }

  // Create sample notifications
  List<NotificationModel> _createSampleNotifications(String userId) {
    return [
      NotificationModel(
        id: 'notif_1',
        toUserId: userId,
        type: 'new_bid',
        title: 'New Bid Received',
        message: 'You have received a new bid for your cement requirement',
        data: {'requirement_id': 'req_1', 'bid_amount': '25000'},
        createdAt: DateTime.now().subtract(const Duration(minutes: 30)),
        read: false,
      ),
      NotificationModel(
        id: 'notif_2',
        toUserId: userId,
        type: 'order_update',
        title: 'Order Confirmed',
        message: 'Your order for steel bars has been confirmed by the vendor',
        data: {'order_id': 'order_1'},
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
        read: false,
      ),
      NotificationModel(
        id: 'notif_3',
        toUserId: userId,
        type: 'promotion',
        title: 'Special Offer',
        message: 'Get 10% off on all cement orders this week!',
        data: {'promo_code': 'CEMENT10'},
        createdAt: DateTime.now().subtract(const Duration(hours: 6)),
        read: true,
        readAt: DateTime.now().subtract(const Duration(hours: 4)),
      ),
      NotificationModel(
        id: 'notif_4',
        toUserId: userId,
        type: 'order_delivered',
        title: 'Order Delivered',
        message: 'Your brick order has been successfully delivered',
        data: {'order_id': 'order_2'},
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        read: true,
        readAt: DateTime.now().subtract(const Duration(hours: 20)),
      ),
    ];
  }

  // Mark notification as read
  Future<void> markAsRead(String notificationId) async {
    try {
      await Future.delayed(const Duration(milliseconds: 200)); // Simulate network delay

      // Update local notification
      final index = _notifications.indexWhere((n) => n.id == notificationId);
      if (index != -1) {
        _notifications[index] = _notifications[index].copyWith(
          read: true,
          readAt: DateTime.now(),
        );
        _updateUnreadCount();
        notifyListeners();
      }
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
    }
  }

  // Mark all notifications as read
  Future<void> markAllAsRead(String userId) async {
    try {
      await Future.delayed(const Duration(milliseconds: 300)); // Simulate network delay

      // Update local notifications
      for (int i = 0; i < _notifications.length; i++) {
        if (!_notifications[i].read) {
          _notifications[i] = _notifications[i].copyWith(
            read: true,
            readAt: DateTime.now(),
          );
        }
      }

      _updateUnreadCount();
      notifyListeners();
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
    }
  }

  // Delete notification
  Future<void> deleteNotification(String notificationId) async {
    try {
      await Future.delayed(const Duration(milliseconds: 200)); // Simulate network delay

      // Remove from local list
      _notifications.removeWhere((n) => n.id == notificationId);
      _updateUnreadCount();
      notifyListeners();
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
    }
  }

  // Send notification to user (mock implementation)
  Future<bool> sendNotificationToUser({
    required String toUserId,
    required String type,
    required String title,
    required String message,
    Map<String, dynamic>? data,
  }) async {
    try {
      await Future.delayed(const Duration(milliseconds: 300)); // Simulate network delay

      // Add to local notifications
      final notification = NotificationModel(
        id: 'notif_${DateTime.now().millisecondsSinceEpoch}',
        toUserId: toUserId,
        type: type,
        title: title,
        message: message,
        data: data ?? {},
        createdAt: DateTime.now(),
        read: false,
      );

      _notifications.insert(0, notification);
      _updateUnreadCount();
      notifyListeners();

      return true;
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Update unread count
  void _updateUnreadCount() {
    _unreadCount = _notifications.where((n) => !n.read).length;
  }

  // Get notifications by type
  List<NotificationModel> getNotificationsByType(String type) {
    return _notifications.where((n) => n.type == type).toList();
  }

  // Get recent notifications (last 24 hours)
  List<NotificationModel> getRecentNotifications() {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return _notifications.where((n) => n.createdAt.isAfter(yesterday)).toList();
  }

  // Clear error
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Dispose
  @override
  void dispose() {
    // _notificationSubscription?.cancel();
    super.dispose();
  }
}
