import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../utils/app_colors.dart';
import 'customer_login_screen.dart';
import 'customer_home_screen.dart';

class CustomerApp extends StatelessWidget {
  const CustomerApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        // Check if user is authenticated as customer
        if (authProvider.isAuthenticated && 
            authProvider.currentUser != null &&
            authProvider.currentUser!.role == 'customer') {
          return const CustomerHomeScreen();
        }
        
        return const CustomerLoginScreen();
      },
    );
  }
}
