import 'package:flutter/material.dart';
// import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/order_model.dart';
import '../models/cart_model.dart';
import '../services/sample_data_service.dart';

class OrderProvider extends ChangeNotifier {
  // final FirebaseFirestore _firestore = FirebaseService.firestore;
  
  List<OrderModel> _orders = [];
  List<OrderModel> _vendorOrders = [];
  CartModel _cart = CartModel.empty();
  bool _isLoading = false;
  String? _errorMessage;
  OrderModel? _currentOrder;

  // Getters
  List<OrderModel> get orders => _orders;
  List<OrderModel> get vendorOrders => _vendorOrders;
  CartModel get cart => _cart;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  OrderModel? get currentOrder => _currentOrder;
  int get cartItemCount => _cart.items.length;
  double get cartTotal => _cart.totalAmount;

  // Cart Management
  void addToCart(CartItemModel item) {
    final existingIndex = _cart.items.indexWhere(
      (cartItem) => cartItem.inventoryId == item.inventoryId,
    );

    if (existingIndex != -1) {
      // Update quantity if item already exists
      _cart.items[existingIndex] = _cart.items[existingIndex].copyWith(
        quantity: _cart.items[existingIndex].quantity + item.quantity,
      );
    } else {
      // Add new item
      _cart.items.add(item);
    }

    _updateCartTotal();
    notifyListeners();
  }

  void removeFromCart(String inventoryId) {
    _cart.items.removeWhere((item) => item.inventoryId == inventoryId);
    _updateCartTotal();
    notifyListeners();
  }

  void updateCartItemQuantity(String inventoryId, int quantity) {
    if (quantity <= 0) {
      removeFromCart(inventoryId);
      return;
    }

    final index = _cart.items.indexWhere(
      (item) => item.inventoryId == inventoryId,
    );

    if (index != -1) {
      _cart.items[index] = _cart.items[index].copyWith(quantity: quantity);
      _updateCartTotal();
      notifyListeners();
    }
  }

  void clearCart() {
    _cart = CartModel.empty();
    notifyListeners();
  }

  void _updateCartTotal() {
    _cart = _cart.copyWith(
      totalAmount: _cart.items.fold(
        0.0,
        (total, item) => total! + (item.price * item.quantity),
      ),
    );
  }

  // Order Management
  Future<bool> placeOrder({
    required String userId,
    required String deliveryAddress,
    required Map<String, dynamic> location,
    String? notes,
  }) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      if (_cart.items.isEmpty) {
        throw Exception('Cart is empty');
      }

      // Group items by vendor
      final vendorGroups = <String, List<CartItemModel>>{};
      for (final item in _cart.items) {
        if (!vendorGroups.containsKey(item.vendorId)) {
          vendorGroups[item.vendorId] = [];
        }
        vendorGroups[item.vendorId]!.add(item);
      }

      // Create separate orders for each vendor
      await Future.delayed(const Duration(milliseconds: 500)); // Simulate network delay
      final orderIds = <String>[];

      for (final vendorEntry in vendorGroups.entries) {
        final vendorId = vendorEntry.key;
        final vendorItems = vendorEntry.value;

        final vendorTotal = vendorItems.fold(
          0.0,
          (total, item) => total + (item.price * item.quantity),
        );

        final orderId = 'order_${DateTime.now().millisecondsSinceEpoch}_$vendorId';
        final order = OrderModel(
          id: orderId,
          userId: userId,
          vendorId: vendorId,
          vendorName: vendorItems.first.vendorName,
          items: vendorItems,
          totalAmount: vendorTotal,
          deliveryAddress: deliveryAddress,
          location: location,
          status: 'placed',
          paymentStatus: 'pending',
          createdAt: DateTime.now(),
          notes: notes,
        );

        // Add to local orders
        _orders.insert(0, order);
        orderIds.add(orderId);
      }

      // Clear cart after successful order
      clearCart();

      // Load user orders to refresh the list
      await loadUserOrders(userId);

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _errorMessage = e.toString();
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  // Load orders for a user
  Future<void> loadUserOrders(String userId) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      await Future.delayed(const Duration(milliseconds: 300)); // Simulate network delay

      // Create sample orders if none exist
      if (_orders.isEmpty) {
        _orders = _createSampleOrders(userId);
      }

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _errorMessage = e.toString();
      _isLoading = false;
      notifyListeners();
    }
  }

  // Create sample orders
  List<OrderModel> _createSampleOrders(String userId) {
    return [
      OrderModel(
        id: 'order_1',
        userId: userId,
        vendorId: 'vendor_1',
        vendorName: 'Rajesh Building Materials',
        items: [
          CartItemModel(
            inventoryId: 'inv_1',
            vendorId: 'vendor_1',
            vendorName: 'Rajesh Building Materials',
            materialType: 'cement',
            description: 'ACC Cement - Grade 53',
            price: 350.0,
            quantity: 20,
            unit: 'bags',
          ),
        ],
        totalAmount: 7000.0,
        deliveryAddress: '123 Construction Site, Mumbai',
        location: {'city': 'Mumbai', 'state': 'Maharashtra'},
        status: 'delivered',
        paymentStatus: 'paid',
        createdAt: DateTime.now().subtract(const Duration(days: 5)),
        deliveryEta: DateTime.now().subtract(const Duration(days: 3)),
      ),
      OrderModel(
        id: 'order_2',
        userId: userId,
        vendorId: 'vendor_2',
        vendorName: 'Sharma Construction Supplies',
        items: [
          CartItemModel(
            inventoryId: 'inv_3',
            vendorId: 'vendor_2',
            vendorName: 'Sharma Construction Supplies',
            materialType: 'steel',
            description: 'TMT Steel Bars - 12mm',
            price: 55.0,
            quantity: 500,
            unit: 'kg',
          ),
        ],
        totalAmount: 27500.0,
        deliveryAddress: '123 Construction Site, Mumbai',
        location: {'city': 'Mumbai', 'state': 'Maharashtra'},
        status: 'confirmed',
        paymentStatus: 'pending',
        createdAt: DateTime.now().subtract(const Duration(days: 2)),
        deliveryEta: DateTime.now().add(const Duration(days: 1)),
      ),
    ];
  }

  // Load orders for a vendor
  Future<void> loadVendorOrders(String vendorId) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      await Future.delayed(const Duration(milliseconds: 300)); // Simulate network delay

      // Create sample vendor orders if none exist
      if (_vendorOrders.isEmpty) {
        _vendorOrders = _createSampleVendorOrders(vendorId);
      }

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _errorMessage = e.toString();
      _isLoading = false;
      notifyListeners();
    }
  }

  // Create sample vendor orders
  List<OrderModel> _createSampleVendorOrders(String vendorId) {
    return [
      OrderModel(
        id: 'vendor_order_1',
        userId: 'user_1',
        vendorId: vendorId,
        vendorName: 'Your Business',
        items: [
          CartItemModel(
            inventoryId: 'inv_1',
            vendorId: vendorId,
            vendorName: 'Your Business',
            materialType: 'cement',
            description: 'ACC Cement - Grade 53',
            price: 350.0,
            quantity: 50,
            unit: 'bags',
          ),
        ],
        totalAmount: 17500.0,
        deliveryAddress: '456 Building Site, Delhi',
        location: {'city': 'Delhi', 'state': 'Delhi'},
        status: 'placed',
        paymentStatus: 'pending',
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
      ),
    ];
  }

  // Update order status
  Future<bool> updateOrderStatus(String orderId, String status) async {
    try {
      await Future.delayed(const Duration(milliseconds: 300)); // Simulate network delay

      // Update local orders
      _updateLocalOrderStatus(orderId, status);

      notifyListeners();
      return true;
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Accept order (for vendors)
  Future<bool> acceptOrder(String orderId) async {
    return await updateOrderStatus(orderId, 'confirmed');
  }

  // Reject order (for vendors)
  Future<bool> rejectOrder(String orderId, String reason) async {
    try {
      await Future.delayed(const Duration(milliseconds: 300)); // Simulate network delay

      _updateLocalOrderStatus(orderId, 'cancelled');
      notifyListeners();
      return true;
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Cancel order (for users)
  Future<bool> cancelOrder(String orderId, String reason) async {
    try {
      final order = getOrderById(orderId);
      if (order == null) return false;

      // Only allow cancellation if order is not yet processed
      if (!['placed', 'confirmed'].contains(order.status)) {
        throw Exception('Order cannot be cancelled at this stage');
      }

      await Future.delayed(const Duration(milliseconds: 300)); // Simulate network delay

      _updateLocalOrderStatus(orderId, 'cancelled');
      notifyListeners();
      return true;
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Get order by ID
  OrderModel? getOrderById(String orderId) {
    try {
      return _orders.firstWhere((order) => order.id == orderId);
    } catch (e) {
      try {
        return _vendorOrders.firstWhere((order) => order.id == orderId);
      } catch (e) {
        return null;
      }
    }
  }

  // Update local order status
  void _updateLocalOrderStatus(String orderId, String status) {
    final orderIndex = _orders.indexWhere((order) => order.id == orderId);
    if (orderIndex != -1) {
      _orders[orderIndex] = _orders[orderIndex].copyWith(status: status);
    }

    final vendorOrderIndex = _vendorOrders.indexWhere((order) => order.id == orderId);
    if (vendorOrderIndex != -1) {
      _vendorOrders[vendorOrderIndex] = _vendorOrders[vendorOrderIndex].copyWith(status: status);
    }
  }

  // Get orders by status
  List<OrderModel> getOrdersByStatus(String status, {bool isVendor = false}) {
    final orderList = isVendor ? _vendorOrders : _orders;
    return orderList.where((order) => order.status == status).toList();
  }

  // Get order statistics for vendor
  Map<String, dynamic> getVendorOrderStats() {
    final totalOrders = _vendorOrders.length;
    final pendingOrders = _vendorOrders.where((o) => o.status == 'placed').length;
    final completedOrders = _vendorOrders.where((o) => o.status == 'delivered').length;
    final totalRevenue = _vendorOrders
        .where((o) => o.status == 'delivered')
        .fold(0.0, (sum, order) => sum + order.totalAmount);

    return {
      'totalOrders': totalOrders,
      'pendingOrders': pendingOrders,
      'completedOrders': completedOrders,
      'totalRevenue': totalRevenue,
    };
  }

  // Clear error
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Set current order for tracking
  void setCurrentOrder(OrderModel order) {
    _currentOrder = order;
    notifyListeners();
  }

  // Clear current order
  void clearCurrentOrder() {
    _currentOrder = null;
    notifyListeners();
  }
}
