{"name": "buildbid-backend", "version": "1.0.0", "description": "Production-ready backend for BuildBid construction marketplace", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "seed": "node scripts/seedDatabase.js", "migrate": "node scripts/migrate.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "helmet": "^7.0.0", "express-rate-limit": "^6.10.0", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "cloudinary": "^1.40.0", "nodemailer": "^6.9.4", "twilio": "^4.15.0", "socket.io": "^4.7.2", "redis": "^4.6.7", "compression": "^1.7.4", "morgan": "^1.10.0", "dotenv": "^16.3.1", "swagger-ui-express": "^5.0.0", "swagger-jsdoc": "^6.2.8", "node-cron": "^3.0.2", "geolib": "^3.3.4", "razorpay": "^2.9.2", "firebase-admin": "^11.10.1"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}