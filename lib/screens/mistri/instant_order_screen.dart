import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/language_provider.dart';
import '../../utils/app_colors.dart';
import '../../utils/constants.dart';
import '../../widgets/custom_search_field.dart';

class InstantOrderScreen extends StatefulWidget {
  final String? initialCategory;
  final String? productId;

  const InstantOrderScreen({
    Key? key,
    this.initialCategory,
    this.productId,
  }) : super(key: key);

  @override
  State<InstantOrderScreen> createState() => _InstantOrderScreenState();
}

class _InstantOrderScreenState extends State<InstantOrderScreen> {
  final _searchController = TextEditingController();
  String? _selectedCategory;
  List<String> _filteredProducts = [];

  @override
  void initState() {
    super.initState();
    _selectedCategory = widget.initialCategory;
    _loadProducts();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _loadProducts() {
    // Mock products for demonstration
    _filteredProducts = [
      'Red Clay Bricks - Premium Quality',
      'Fly Ash Bricks - Eco Friendly',
      'OPC 43 Grade Cement - 50kg',
      'PPC Cement - 50kg',
      'TMT Bars 12mm - Per Ton',
      'TMT Bars 16mm - Per Ton',
      'River Sand - Per Cubic Feet',
      'M-Sand - Per Cubic Feet',
    ];
    
    if (_selectedCategory != null) {
      _filterByCategory(_selectedCategory!);
    }
  }

  void _filterByCategory(String category) {
    setState(() {
      _selectedCategory = category;
      // Filter products based on category
      switch (category) {
        case 'Bricks':
          _filteredProducts = _filteredProducts.where((p) => p.contains('Brick')).toList();
          break;
        case 'Cement':
          _filteredProducts = _filteredProducts.where((p) => p.contains('Cement')).toList();
          break;
        case 'Steel Rods':
          _filteredProducts = _filteredProducts.where((p) => p.contains('TMT')).toList();
          break;
        case 'Sand':
          _filteredProducts = _filteredProducts.where((p) => p.contains('Sand')).toList();
          break;
        default:
          _loadProducts();
      }
    });
  }

  void _searchProducts(String query) {
    setState(() {
      if (query.isEmpty) {
        _loadProducts();
      } else {
        _filteredProducts = _filteredProducts
            .where((p) => p.toLowerCase().contains(query.toLowerCase()))
            .toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(languageProvider.getText('instant_order')),
        backgroundColor: AppColors.mistriColor,
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          // Search Bar
          Padding(
            padding: const EdgeInsets.all(16),
            child: CustomSearchField(
              controller: _searchController,
              hint: 'Search materials...',
              onChanged: _searchProducts,
            ),
          ),

          // Category Filter
          SizedBox(
            height: 50,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: AppConstants.materialTypes.length + 1,
              itemBuilder: (context, index) {
                if (index == 0) {
                  return _buildCategoryChip('All', _selectedCategory == null);
                }
                final category = AppConstants.materialTypes[index - 1];
                return _buildCategoryChip(category, _selectedCategory == category);
              },
            ),
          ),

          const SizedBox(height: 16),

          // Products List
          Expanded(
            child: _filteredProducts.isEmpty
                ? const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.search_off, size: 64, color: AppColors.textSecondary),
                        SizedBox(height: 16),
                        Text(
                          'No products found',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: AppColors.textSecondary,
                          ),
                        ),
                        SizedBox(height: 8),
                        Text(
                          'Try searching with different keywords',
                          style: TextStyle(
                            fontSize: 14,
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  )
                : ListView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    itemCount: _filteredProducts.length,
                    itemBuilder: (context, index) {
                      final product = _filteredProducts[index];
                      return _buildProductCard(product);
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryChip(String category, bool isSelected) {
    return Container(
      margin: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Text(category),
        selected: isSelected,
        onSelected: (selected) {
          if (category == 'All') {
            setState(() {
              _selectedCategory = null;
            });
            _loadProducts();
          } else {
            _filterByCategory(category);
          }
        },
        selectedColor: AppColors.mistriColor.withOpacity(0.2),
        checkmarkColor: AppColors.mistriColor,
        labelStyle: TextStyle(
          color: isSelected ? AppColors.mistriColor : AppColors.textSecondary,
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
        ),
      ),
    );
  }

  Widget _buildProductCard(String productName) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.border),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Product Image Placeholder
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: AppColors.greyLight,
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.construction,
              color: AppColors.textSecondary,
              size: 24,
            ),
          ),

          const SizedBox(width: 16),

          // Product Details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  productName,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                const Text(
                  'Available for instant delivery',
                  style: TextStyle(
                    fontSize: 12,
                    color: AppColors.success,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    const Text(
                      '₹',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: AppColors.mistriColor,
                      ),
                    ),
                    const Text(
                      '2,500',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppColors.mistriColor,
                      ),
                    ),
                    const Text(
                      '/unit',
                      style: TextStyle(
                        fontSize: 12,
                        color: AppColors.textSecondary,
                      ),
                    ),
                    const Spacer(),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: AppColors.success.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.star, size: 12, color: AppColors.success),
                          SizedBox(width: 2),
                          Text(
                            '4.5',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              color: AppColors.success,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          const SizedBox(width: 16),

          // Add to Cart Button
          ElevatedButton(
            onPressed: () {
              _showQuantityDialog(productName);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.mistriColor,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            ),
            child: const Text(
              'ADD',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showQuantityDialog(String productName) {
    final quantityController = TextEditingController(text: '1');
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Quantity'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              productName,
              style: const TextStyle(
                fontSize: 14,
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: quantityController,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'Quantity',
                border: OutlineInputBorder(),
                suffixText: 'units',
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Added ${quantityController.text} units to cart'),
                  backgroundColor: AppColors.success,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.mistriColor,
            ),
            child: const Text('Add to Cart'),
          ),
        ],
      ),
    );
  }
}
