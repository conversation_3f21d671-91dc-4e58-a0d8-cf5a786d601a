import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/order_provider.dart';
import '../../providers/notification_provider.dart';
import '../../utils/app_colors.dart';

class VendorOrdersScreen extends StatefulWidget {
  const VendorOrdersScreen({Key? key}) : super(key: key);

  @override
  State<VendorOrdersScreen> createState() => _VendorOrdersScreenState();
}

class _VendorOrdersScreenState extends State<VendorOrdersScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Orders'),
        backgroundColor: AppColors.secondary,
        foregroundColor: Colors.white,
        elevation: 0,
        automaticallyImplyLeading: false,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          isScrollable: true,
          tabs: const [
            Tab(text: 'New'),
            Tab(text: 'Preparing'),
            Tab(text: 'Delivered'),
            Tab(text: 'All'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildOrdersList('new'),
          _buildOrdersList('preparing'),
          _buildOrdersList('delivered'),
          _buildOrdersList('all'),
        ],
      ),
    );
  }

  Widget _buildOrdersList(String status) {
    return Consumer2<OrderProvider, NotificationProvider>(
      builder: (context, orderProvider, notificationProvider, child) {
        // Sample vendor orders for demo
        final orders = _getSampleVendorOrders().where((order) {
          switch (status) {
            case 'new':
              return order['status'] == 'pending';
            case 'preparing':
              return ['confirmed', 'preparing', 'out_for_delivery'].contains(order['status']);
            case 'delivered':
              return order['status'] == 'delivered';
            case 'all':
              return true;
            default:
              return false;
          }
        }).toList();

        if (orders.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.shopping_bag_outlined,
                  size: 64,
                  color: AppColors.textSecondary,
                ),
                const SizedBox(height: 16),
                Text(
                  'No ${status == 'all' ? '' : status} orders',
                  style: TextStyle(
                    fontSize: 18,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: orders.length,
          itemBuilder: (context, index) {
            final order = orders[index];
            return _buildVendorOrderCard(order, notificationProvider);
          },
        );
      },
    );
  }

  Widget _buildVendorOrderCard(Map<String, dynamic> order, NotificationProvider notificationProvider) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Order #${order['id'].substring(order['id'].length - 6)}',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getStatusColor(order['status']).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    _getStatusText(order['status']),
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: _getStatusColor(order['status']),
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            Row(
              children: [
                Icon(Icons.person, size: 16, color: AppColors.textSecondary),
                const SizedBox(width: 8),
                Text(
                  order['customerName'],
                  style: TextStyle(
                    fontSize: 14,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 8),
            
            Row(
              children: [
                Icon(Icons.phone, size: 16, color: AppColors.textSecondary),
                const SizedBox(width: 8),
                Text(
                  order['customerPhone'],
                  style: TextStyle(
                    fontSize: 14,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 8),
            
            Row(
              children: [
                Icon(Icons.location_on, size: 16, color: AppColors.textSecondary),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    order['deliveryAddress'],
                    style: TextStyle(
                      fontSize: 14,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            // Order Items
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.greyLight.withOpacity(0.3),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Items:',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  ...order['items'].map<Widget>((item) => Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          '${item['name']} x ${item['quantity']}',
                          style: const TextStyle(fontSize: 14),
                        ),
                        Text(
                          '₹${item['total']}',
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  )).toList(),
                ],
              ),
            ),
            
            const SizedBox(height: 12),
            
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  order['date'],
                  style: TextStyle(
                    fontSize: 14,
                    color: AppColors.textSecondary,
                  ),
                ),
                Text(
                  '₹${order['total'].toStringAsFixed(0)}',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.secondary,
                  ),
                ),
              ],
            ),
            
            if (order['status'] == 'pending') ...[
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () {
                        _updateOrderStatus(order['id'], 'cancelled', notificationProvider);
                      },
                      style: OutlinedButton.styleFrom(
                        foregroundColor: AppColors.error,
                        side: BorderSide(color: AppColors.error),
                      ),
                      child: const Text('Reject'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        _updateOrderStatus(order['id'], 'confirmed', notificationProvider);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.success,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('Accept'),
                    ),
                  ),
                ],
              ),
            ] else if (order['status'] == 'confirmed') ...[
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    _updateOrderStatus(order['id'], 'preparing', notificationProvider);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.secondary,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Start Preparing'),
                ),
              ),
            ] else if (order['status'] == 'preparing') ...[
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    _updateOrderStatus(order['id'], 'out_for_delivery', notificationProvider);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Mark Out for Delivery'),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _updateOrderStatus(String orderId, String newStatus, NotificationProvider notificationProvider) {
    setState(() {
      // Update order status in the sample data
      final orders = _getSampleVendorOrders();
      final orderIndex = orders.indexWhere((order) => order['id'] == orderId);
      if (orderIndex != -1) {
        orders[orderIndex]['status'] = newStatus;
      }
    });

    // Send notification to customer
    notificationProvider.sendOrderUpdateNotification(orderId, newStatus);

    // Show success message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Order status updated to ${_getStatusText(newStatus)}'),
        backgroundColor: AppColors.success,
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'pending':
        return AppColors.warning;
      case 'confirmed':
      case 'preparing':
        return AppColors.info;
      case 'out_for_delivery':
        return AppColors.primary;
      case 'delivered':
        return AppColors.success;
      case 'cancelled':
        return AppColors.error;
      default:
        return AppColors.textSecondary;
    }
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'pending':
        return 'New Order';
      case 'confirmed':
        return 'Confirmed';
      case 'preparing':
        return 'Preparing';
      case 'out_for_delivery':
        return 'Out for Delivery';
      case 'delivered':
        return 'Delivered';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status;
    }
  }

  List<Map<String, dynamic>> _getSampleVendorOrders() {
    return [
      {
        'id': 'ORD${DateTime.now().millisecondsSinceEpoch}001',
        'customerName': 'Rajesh Kumar',
        'customerPhone': '+91 9876543210',
        'deliveryAddress': 'Sector 15, Gurgaon, Haryana',
        'status': 'pending',
        'date': 'Today, 2:30 PM',
        'total': 2850.0,
        'items': [
          {'name': 'Red Clay Bricks', 'quantity': 100, 'total': 800},
          {'name': 'OPC Cement', 'quantity': 5, 'total': 1850},
          {'name': 'River Sand', 'quantity': 2, 'total': 200},
        ],
      },
      {
        'id': 'ORD${DateTime.now().millisecondsSinceEpoch}002',
        'customerName': 'Priya Sharma',
        'customerPhone': '+91 9876543211',
        'deliveryAddress': 'Cyber City, Gurgaon, Haryana',
        'status': 'confirmed',
        'date': 'Today, 1:15 PM',
        'total': 1500.0,
        'items': [
          {'name': 'TMT Steel Bars', 'quantity': 20, 'total': 1300},
          {'name': 'Binding Wire', 'quantity': 5, 'total': 200},
        ],
      },
      {
        'id': 'ORD${DateTime.now().millisecondsSinceEpoch}003',
        'customerName': 'Amit Singh',
        'customerPhone': '+91 9876543212',
        'deliveryAddress': 'DLF Phase 2, Gurgaon, Haryana',
        'status': 'preparing',
        'date': 'Today, 11:00 AM',
        'total': 3200.0,
        'items': [
          {'name': 'Concrete Blocks', 'quantity': 50, 'total': 2500},
          {'name': 'Mortar Mix', 'quantity': 10, 'total': 700},
        ],
      },
      {
        'id': 'ORD${DateTime.now().millisecondsSinceEpoch}004',
        'customerName': 'Sunita Devi',
        'customerPhone': '+91 9876543213',
        'deliveryAddress': 'Sector 18, Gurgaon, Haryana',
        'status': 'delivered',
        'date': 'Yesterday, 3:45 PM',
        'total': 1800.0,
        'items': [
          {'name': 'Wall Tiles', 'quantity': 25, 'total': 1500},
          {'name': 'Tile Adhesive', 'quantity': 3, 'total': 300},
        ],
      },
    ];
  }
}
