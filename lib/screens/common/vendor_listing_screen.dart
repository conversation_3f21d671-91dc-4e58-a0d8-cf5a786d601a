import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/vendor_provider.dart';
import '../../providers/location_provider.dart';
import '../../utils/app_colors.dart';
import '../../widgets/custom_search_field.dart';
import '../../models/vendor_model.dart';
import 'vendor_detail_screen.dart';

class VendorListingScreen extends StatefulWidget {
  final String? category;

  const VendorListingScreen({
    Key? key,
    this.category,
  }) : super(key: key);

  @override
  State<VendorListingScreen> createState() => _VendorListingScreenState();
}

class _VendorListingScreenState extends State<VendorListingScreen>
    with TickerProviderStateMixin {
  final _searchController = TextEditingController();
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  bool _showFilters = false;
  String _sortBy = 'distance'; // distance, rating, delivery_time, price

  final List<String> _categories = [
    'All', 'Bricks', 'Cement', 'Steel', 'Sand', 'Paints', 'Tiles'
  ];

  final List<String> _priceRanges = [
    'All', 'Budget (₹)', 'Mid-range (₹₹)', 'Premium (₹₹₹)'
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadVendors();
      if (widget.category != null) {
        context.read<VendorProvider>().setCategoryFilter(widget.category!);
      }
    });

    _animationController.forward();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadVendors() async {
    final vendorProvider = context.read<VendorProvider>();
    final locationProvider = context.read<LocationProvider>();
    
    await vendorProvider.loadVendors();
    
    if (locationProvider.currentLocation != null) {
      await vendorProvider.loadNearbyVendors(locationProvider.currentLocation!);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.category != null ? '${widget.category} Vendors' : 'All Vendors'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(_showFilters ? Icons.filter_list : Icons.filter_list_outlined),
            onPressed: () {
              setState(() {
                _showFilters = !_showFilters;
              });
            },
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.sort),
            onSelected: (value) {
              setState(() {
                _sortBy = value;
              });
              _sortVendors(value);
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'distance',
                child: Row(
                  children: [
                    Icon(Icons.location_on),
                    SizedBox(width: 8),
                    Text('Distance'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'rating',
                child: Row(
                  children: [
                    Icon(Icons.star),
                    SizedBox(width: 8),
                    Text('Rating'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'delivery_time',
                child: Row(
                  children: [
                    Icon(Icons.access_time),
                    SizedBox(width: 8),
                    Text('Delivery Time'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'price',
                child: Row(
                  children: [
                    Icon(Icons.currency_rupee),
                    SizedBox(width: 8),
                    Text('Price'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Column(
          children: [
            // Search Section
            Container(
              color: AppColors.primary,
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
              child: CustomSearchField(
                controller: _searchController,
                hint: 'Search vendors, materials...',
                onChanged: (value) {
                  context.read<VendorProvider>().searchVendors(value);
                },
              ),
            ),

            // Filters Section
            if (_showFilters) _buildFiltersSection(),

            // Quick Filter Chips
            Container(
              height: 50,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: ListView(
                scrollDirection: Axis.horizontal,
                children: [
                  _buildFilterChip('Open Now', context.read<VendorProvider>().openNow, () {
                    context.read<VendorProvider>().setOpenNowFilter(
                        !context.read<VendorProvider>().openNow);
                  }),
                  const SizedBox(width: 8),
                  _buildFilterChip('Free Delivery', false, () {
                    // TODO: Implement free delivery filter
                  }),
                  const SizedBox(width: 8),
                  _buildFilterChip('Fast Delivery', false, () {
                    // TODO: Implement fast delivery filter
                  }),
                  const SizedBox(width: 8),
                  _buildFilterChip('Top Rated', false, () {
                    context.read<VendorProvider>().setRatingFilter(4.0);
                  }),
                ],
              ),
            ),

            const Divider(height: 1),

            // Vendors List
            Expanded(
              child: Consumer<VendorProvider>(
                builder: (context, vendorProvider, child) {
                  if (vendorProvider.isLoading) {
                    return _buildLoadingState();
                  }

                  if (vendorProvider.errorMessage != null) {
                    return _buildErrorState(vendorProvider.errorMessage!);
                  }

                  final vendors = vendorProvider.filteredVendors;

                  if (vendors.isEmpty) {
                    return _buildEmptyState();
                  }

                  return RefreshIndicator(
                    onRefresh: _loadVendors,
                    child: ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: vendors.length,
                      itemBuilder: (context, index) {
                        final vendor = vendors[index];
                        return Padding(
                          padding: const EdgeInsets.only(bottom: 16),
                          child: _buildVendorCard(vendor),
                        );
                      },
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFiltersSection() {
    return Container(
      color: Colors.grey.shade50,
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Filters',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          // Category Filter
          const Text('Category', style: TextStyle(fontWeight: FontWeight.w600)),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            children: _categories.map((category) {
              final isSelected = context.watch<VendorProvider>().selectedCategory == category ||
                  (category == 'All' && context.watch<VendorProvider>().selectedCategory.isEmpty);
              return FilterChip(
                label: Text(category),
                selected: isSelected,
                onSelected: (selected) {
                  context.read<VendorProvider>().setCategoryFilter(
                      selected ? (category == 'All' ? '' : category) : '');
                },
                selectedColor: AppColors.primary.withOpacity(0.2),
                checkmarkColor: AppColors.primary,
              );
            }).toList(),
          ),
          
          const SizedBox(height: 16),
          
          // Rating Filter
          const Text('Minimum Rating', style: TextStyle(fontWeight: FontWeight.w600)),
          const SizedBox(height: 8),
          Consumer<VendorProvider>(
            builder: (context, vendorProvider, child) {
              return Slider(
                value: vendorProvider.minRating,
                min: 0.0,
                max: 5.0,
                divisions: 10,
                label: '${vendorProvider.minRating.toStringAsFixed(1)} ⭐',
                onChanged: (value) {
                  vendorProvider.setRatingFilter(value);
                },
                activeColor: AppColors.primary,
              );
            },
          ),
          
          const SizedBox(height: 16),
          
          // Distance Filter
          const Text('Maximum Distance', style: TextStyle(fontWeight: FontWeight.w600)),
          const SizedBox(height: 8),
          Consumer<VendorProvider>(
            builder: (context, vendorProvider, child) {
              return Slider(
                value: vendorProvider.maxDistance,
                min: 1.0,
                max: 50.0,
                divisions: 49,
                label: '${vendorProvider.maxDistance.toStringAsFixed(0)} km',
                onChanged: (value) {
                  vendorProvider.setDistanceFilter(value);
                },
                activeColor: AppColors.primary,
              );
            },
          ),
          
          const SizedBox(height: 16),
          
          // Clear Filters Button
          SizedBox(
            width: double.infinity,
            child: OutlinedButton(
              onPressed: () {
                context.read<VendorProvider>().clearFilters();
              },
              child: const Text('Clear All Filters'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, bool isSelected, VoidCallback onTap) {
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (_) => onTap(),
      selectedColor: AppColors.primary.withOpacity(0.2),
      checkmarkColor: AppColors.primary,
    );
  }

  Widget _buildVendorCard(VendorModel vendor) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => VendorDetailScreen(vendor: vendor),
            ),
          );
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  // Vendor Image
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color: AppColors.greyLight,
                      borderRadius: BorderRadius.circular(8),
                      image: vendor.profileImage != null
                          ? DecorationImage(
                              image: NetworkImage(vendor.profileImage!),
                              fit: BoxFit.cover,
                            )
                          : null,
                    ),
                    child: vendor.profileImage == null
                        ? Icon(
                            Icons.store,
                            color: AppColors.textSecondary,
                            size: 24,
                          )
                        : null,
                  ),
                  const SizedBox(width: 16),

                  // Vendor Info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                vendor.displayName,
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            if (vendor.isVerified)
                              Container(
                                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                decoration: BoxDecoration(
                                  color: AppColors.success.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      Icons.verified,
                                      size: 12,
                                      color: AppColors.success,
                                    ),
                                    const SizedBox(width: 2),
                                    Text(
                                      'Verified',
                                      style: TextStyle(
                                        fontSize: 10,
                                        fontWeight: FontWeight.w600,
                                        color: AppColors.success,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Text(
                          vendor.primarySpecialization,
                          style: TextStyle(
                            fontSize: 14,
                            color: AppColors.textSecondary,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Icon(
                              Icons.star,
                              size: 16,
                              color: AppColors.warning,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              vendor.rating.toString(),
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            Text(
                              ' (${vendor.reviewCount})',
                              style: TextStyle(
                                fontSize: 12,
                                color: AppColors.textSecondary,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Text(
                              vendor.priceRangeIndicator,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: AppColors.primary,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  // Status Indicator
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: vendor.isCurrentlyOpen
                          ? AppColors.success.withOpacity(0.1)
                          : AppColors.error.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      vendor.statusText,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: vendor.isCurrentlyOpen ? AppColors.success : AppColors.error,
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Additional Info
              Row(
                children: [
                  if (vendor.distance != null) ...[
                    Icon(
                      Icons.location_on,
                      size: 16,
                      color: AppColors.textSecondary,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      vendor.distanceText,
                      style: TextStyle(
                        fontSize: 12,
                        color: AppColors.textSecondary,
                      ),
                    ),
                    const SizedBox(width: 16),
                  ],
                  Icon(
                    Icons.access_time,
                    size: 16,
                    color: AppColors.textSecondary,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    vendor.deliveryTimeText,
                    style: TextStyle(
                      fontSize: 12,
                      color: AppColors.textSecondary,
                    ),
                  ),
                  const Spacer(),
                  if (vendor.freeDeliveryAvailable)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: AppColors.primary.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        'Free Delivery',
                        style: TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.w600,
                          color: AppColors.primary,
                        ),
                      ),
                    ),
                ],
              ),

              if (vendor.minimumOrderValue > 0) ...[
                const SizedBox(height: 8),
                Text(
                  'Min order: ₹${vendor.minimumOrderValue.toStringAsFixed(0)}',
                  style: TextStyle(
                    fontSize: 12,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: 5,
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: Container(
            height: 120,
            decoration: BoxDecoration(
              color: AppColors.greyLight,
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        );
      },
    );
  }

  Widget _buildErrorState(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: AppColors.error,
          ),
          const SizedBox(height: 16),
          Text(
            'Error loading vendors',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: TextStyle(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _loadVendors,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.store_outlined,
            size: 64,
            color: AppColors.textSecondary,
          ),
          const SizedBox(height: 16),
          Text(
            'No vendors found',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Try adjusting your filters or search terms',
            style: TextStyle(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () {
              context.read<VendorProvider>().clearFilters();
            },
            child: const Text('Clear Filters'),
          ),
        ],
      ),
    );
  }

  void _sortVendors(String sortBy) {
    final vendorProvider = context.read<VendorProvider>();
    List<VendorModel> vendors = List.from(vendorProvider.filteredVendors);

    switch (sortBy) {
      case 'distance':
        vendors.sort((a, b) => (a.distance ?? 0).compareTo(b.distance ?? 0));
        break;
      case 'rating':
        vendors.sort((a, b) => b.rating.compareTo(a.rating));
        break;
      case 'delivery_time':
        vendors.sort((a, b) => a.averageDeliveryTime.compareTo(b.averageDeliveryTime));
        break;
      case 'price':
        vendors.sort((a, b) => a.averagePrice.compareTo(b.averagePrice));
        break;
    }

    // Update the filtered vendors list
    vendorProvider.filteredVendors.clear();
    vendorProvider.filteredVendors.addAll(vendors);
    vendorProvider.notifyListeners();
  }
}
