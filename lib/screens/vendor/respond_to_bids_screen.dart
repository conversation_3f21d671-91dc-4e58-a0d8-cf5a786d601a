import 'package:flutter/material.dart';
import '../../utils/app_colors.dart';

class RespondToBidsScreen extends StatelessWidget {
  final String requirementId;

  const RespondToBidsScreen({
    Key? key,
    required this.requirementId,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Requirements'),
        backgroundColor: AppColors.vendorColor,
        foregroundColor: Colors.white,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.list_alt,
              size: 80,
              color: AppColors.textSecondary,
            ),
            Sized<PERSON><PERSON>(height: 24),
            Text(
              'Bid on Requirements',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Browse and bid on construction requirements',
              style: TextStyle(
                fontSize: 14,
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
