{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "cloud_firestore", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-4.17.5/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-5.0.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_analytics", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-10.10.7/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_auth", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-4.16.0/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-2.32.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_crashlytics", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_crashlytics-3.5.7/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_dynamic_links", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_dynamic_links-5.5.7/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_messaging", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-14.7.10/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_performance", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_performance-0.9.4+7/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_remote_config", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_remote_config-4.4.7/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_storage", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_storage-11.6.5/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "flutter_local_notifications", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-16.3.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "fluttertoast", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "geolocator_apple", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "image_picker_ios", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-4.2.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_apple", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "razorpay_flutter", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/razorpay_flutter-1.4.0/", "native_build": true, "dependencies": ["fluttertoast"], "dev_dependency": false}, {"name": "rive_common", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rive_common-0.2.8/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sentry_flutter", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-7.20.2/", "native_build": true, "dependencies": ["package_info_plus"], "dev_dependency": false}, {"name": "shared_preferences_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sms_autofill", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sms_autofill-2.4.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite_darwin", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_ios", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "workmanager", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/workmanager-0.5.2/", "native_build": true, "dependencies": [], "dev_dependency": false}], "android": [{"name": "cloud_firestore", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-4.17.5/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-5.0.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_analytics", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-10.10.7/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_auth", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-4.16.0/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-2.32.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_crashlytics", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_crashlytics-3.5.7/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_dynamic_links", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_dynamic_links-5.5.7/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_messaging", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-14.7.10/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_performance", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_performance-0.9.4+7/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_remote_config", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_remote_config-4.4.7/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_storage", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_storage-11.6.5/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "flutter_local_notifications", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-16.3.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_plugin_android_lifecycle", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "fluttertoast", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "geolocator_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_android-4.6.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "image_picker_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"], "dev_dependency": false}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-4.2.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-12.1.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "razorpay_flutter", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/razorpay_flutter-1.4.0/", "native_build": true, "dependencies": ["fluttertoast"], "dev_dependency": false}, {"name": "rive_common", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rive_common-0.2.8/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sentry_flutter", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-7.20.2/", "native_build": true, "dependencies": ["package_info_plus"], "dev_dependency": false}, {"name": "shared_preferences_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sms_autofill", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sms_autofill-2.4.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "workmanager", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/workmanager-0.5.2/", "native_build": true, "dependencies": [], "dev_dependency": false}], "macos": [{"name": "cloud_firestore", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-4.17.5/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-5.0.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "file_selector_macos", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_analytics", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-10.10.7/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_auth", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-4.16.0/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-2.32.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_crashlytics", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_crashlytics-3.5.7/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_messaging", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-14.7.10/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_remote_config", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_remote_config-4.4.7/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_storage", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_storage-11.6.5/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "flutter_local_notifications", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-16.3.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "geolocator_apple", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "image_picker_macos", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/", "native_build": false, "dependencies": ["file_selector_macos"], "dev_dependency": false}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-4.2.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "rive_common", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rive_common-0.2.8/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sentry_flutter", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-7.20.2/", "native_build": true, "dependencies": ["package_info_plus"], "dev_dependency": false}, {"name": "shared_preferences_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite_darwin", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_macos", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/", "native_build": true, "dependencies": [], "dev_dependency": false}], "linux": [{"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-5.0.2/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "file_selector_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_local_notifications_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-4.0.1/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "image_picker_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/", "native_build": false, "dependencies": ["file_selector_linux"], "dev_dependency": false}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-4.2.0/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "sentry_flutter", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-7.20.2/", "native_build": true, "dependencies": ["package_info_plus"], "dev_dependency": false}, {"name": "shared_preferences_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/", "native_build": false, "dependencies": ["path_provider_linux"], "dev_dependency": false}, {"name": "url_launcher_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/", "native_build": true, "dependencies": [], "dev_dependency": false}], "windows": [{"name": "cloud_firestore", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore-4.17.5/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-5.0.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "file_selector_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_auth", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-4.16.0/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-2.32.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_storage", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_storage-11.6.5/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "geolocator_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_windows-0.2.5/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "image_picker_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/", "native_build": false, "dependencies": ["file_selector_windows"], "dev_dependency": false}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-4.2.0/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "rive_common", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rive_common-0.2.8/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sentry_flutter", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-7.20.2/", "native_build": true, "dependencies": ["package_info_plus"], "dev_dependency": false}, {"name": "shared_preferences_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/", "native_build": false, "dependencies": ["path_provider_windows"], "dev_dependency": false}, {"name": "url_launcher_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/", "native_build": true, "dependencies": [], "dev_dependency": false}], "web": [{"name": "cloud_firestore_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cloud_firestore_web-3.12.5/", "dependencies": ["firebase_core_web"], "dev_dependency": false}, {"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-5.0.2/", "dependencies": [], "dev_dependency": false}, {"name": "device_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/", "dependencies": [], "dev_dependency": false}, {"name": "firebase_analytics_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics_web-0.5.7+7/", "dependencies": ["firebase_core_web"], "dev_dependency": false}, {"name": "firebase_auth_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_web-5.8.13/", "dependencies": ["firebase_core_web"], "dev_dependency": false}, {"name": "firebase_core_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.23.0/", "dependencies": [], "dev_dependency": false}, {"name": "firebase_messaging_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_web-3.5.18/", "dependencies": ["firebase_core_web"], "dev_dependency": false}, {"name": "firebase_performance_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_performance_web-0.1.6+7/", "dependencies": ["firebase_core_web"], "dev_dependency": false}, {"name": "firebase_remote_config_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_remote_config_web-1.6.7/", "dependencies": ["firebase_core_web"], "dev_dependency": false}, {"name": "firebase_storage_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_storage_web-3.6.22/", "dependencies": ["firebase_core_web"], "dev_dependency": false}, {"name": "fluttertoast", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/", "dependencies": [], "dev_dependency": false}, {"name": "geolocator_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_web-2.2.1/", "dependencies": [], "dev_dependency": false}, {"name": "image_picker_for_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.6/", "dependencies": [], "dev_dependency": false}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-4.2.0/", "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_html", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/", "dependencies": [], "dev_dependency": false}, {"name": "rive_common", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/rive_common-0.2.8/", "dependencies": [], "dev_dependency": false}, {"name": "sentry_flutter", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sentry_flutter-7.20.2/", "dependencies": ["package_info_plus"], "dev_dependency": false}, {"name": "shared_preferences_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/", "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1/", "dependencies": [], "dev_dependency": false}]}, "dependencyGraph": [{"name": "cloud_firestore", "dependencies": ["cloud_firestore_web", "firebase_core"]}, {"name": "cloud_firestore_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "connectivity_plus", "dependencies": []}, {"name": "device_info_plus", "dependencies": []}, {"name": "file_selector_linux", "dependencies": []}, {"name": "file_selector_macos", "dependencies": []}, {"name": "file_selector_windows", "dependencies": []}, {"name": "firebase_analytics", "dependencies": ["firebase_analytics_web", "firebase_core"]}, {"name": "firebase_analytics_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "firebase_auth", "dependencies": ["firebase_auth_web", "firebase_core"]}, {"name": "firebase_auth_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "firebase_core", "dependencies": ["firebase_core_web"]}, {"name": "firebase_core_web", "dependencies": []}, {"name": "firebase_crashlytics", "dependencies": ["firebase_core"]}, {"name": "firebase_dynamic_links", "dependencies": ["firebase_core"]}, {"name": "firebase_messaging", "dependencies": ["firebase_core", "firebase_messaging_web"]}, {"name": "firebase_messaging_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "firebase_performance", "dependencies": ["firebase_core", "firebase_performance_web"]}, {"name": "firebase_performance_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "firebase_remote_config", "dependencies": ["firebase_core", "firebase_remote_config_web"]}, {"name": "firebase_remote_config_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "firebase_storage", "dependencies": ["firebase_core", "firebase_storage_web"]}, {"name": "firebase_storage_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "flutter_local_notifications", "dependencies": ["flutter_local_notifications_linux"]}, {"name": "flutter_local_notifications_linux", "dependencies": []}, {"name": "flutter_plugin_android_lifecycle", "dependencies": []}, {"name": "fluttertoast", "dependencies": []}, {"name": "geolocator", "dependencies": ["geolocator_android", "geolocator_apple", "geolocator_web", "geolocator_windows"]}, {"name": "geolocator_android", "dependencies": []}, {"name": "geolocator_apple", "dependencies": []}, {"name": "geolocator_web", "dependencies": []}, {"name": "geolocator_windows", "dependencies": []}, {"name": "image_picker", "dependencies": ["image_picker_android", "image_picker_for_web", "image_picker_ios", "image_picker_linux", "image_picker_macos", "image_picker_windows"]}, {"name": "image_picker_android", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "image_picker_for_web", "dependencies": []}, {"name": "image_picker_ios", "dependencies": []}, {"name": "image_picker_linux", "dependencies": ["file_selector_linux"]}, {"name": "image_picker_macos", "dependencies": ["file_selector_macos"]}, {"name": "image_picker_windows", "dependencies": ["file_selector_windows"]}, {"name": "package_info_plus", "dependencies": []}, {"name": "path_provider", "dependencies": ["path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_windows"]}, {"name": "path_provider_android", "dependencies": []}, {"name": "path_provider_foundation", "dependencies": []}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}, {"name": "permission_handler", "dependencies": ["permission_handler_android", "permission_handler_apple", "permission_handler_html", "permission_handler_windows"]}, {"name": "permission_handler_android", "dependencies": []}, {"name": "permission_handler_apple", "dependencies": []}, {"name": "permission_handler_html", "dependencies": []}, {"name": "permission_handler_windows", "dependencies": []}, {"name": "razorpay_flutter", "dependencies": ["fluttertoast"]}, {"name": "rive_common", "dependencies": []}, {"name": "sentry_flutter", "dependencies": ["package_info_plus"]}, {"name": "shared_preferences", "dependencies": ["shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "shared_preferences_android", "dependencies": []}, {"name": "shared_preferences_foundation", "dependencies": []}, {"name": "shared_preferences_linux", "dependencies": ["path_provider_linux"]}, {"name": "shared_preferences_web", "dependencies": []}, {"name": "shared_preferences_windows", "dependencies": ["path_provider_windows"]}, {"name": "sms_autofill", "dependencies": []}, {"name": "sqflite", "dependencies": ["sqflite_android", "sqflite_darwin"]}, {"name": "sqflite_android", "dependencies": []}, {"name": "sqflite_darwin", "dependencies": []}, {"name": "url_launcher", "dependencies": ["url_launcher_android", "url_launcher_ios", "url_launcher_linux", "url_launcher_macos", "url_launcher_web", "url_launcher_windows"]}, {"name": "url_launcher_android", "dependencies": []}, {"name": "url_launcher_ios", "dependencies": []}, {"name": "url_launcher_linux", "dependencies": []}, {"name": "url_launcher_macos", "dependencies": []}, {"name": "url_launcher_web", "dependencies": []}, {"name": "url_launcher_windows", "dependencies": []}, {"name": "workmanager", "dependencies": []}], "date_created": "2025-06-13 15:30:03.178435", "version": "3.32.1", "swift_package_manager_enabled": {"ios": false, "macos": false}}