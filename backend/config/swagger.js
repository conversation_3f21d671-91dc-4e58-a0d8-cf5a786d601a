const swaggerJsdoc = require('swagger-jsdoc');

const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'BuildBid API',
      version: '1.0.0',
      description: 'Production-ready API for BuildBid construction marketplace',
      contact: {
        name: 'BuildBid Support',
        email: '<EMAIL>',
        url: 'https://buildbid.com'
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT'
      }
    },
    servers: [
      {
        url: process.env.APP_URL || 'http://localhost:3000',
        description: 'Production server'
      },
      {
        url: 'http://localhost:3000',
        description: 'Development server'
      }
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT'
        }
      },
      schemas: {
        User: {
          type: 'object',
          properties: {
            _id: { type: 'string' },
            name: { type: 'string' },
            phone: { type: 'string' },
            email: { type: 'string' },
            role: { 
              type: 'string',
              enum: ['builder', 'mistri', 'vendor', 'admin']
            },
            location: {
              type: 'object',
              properties: {
                coordinates: {
                  type: 'array',
                  items: { type: 'number' }
                },
                address: { type: 'string' },
                city: { type: 'string' },
                state: { type: 'string' },
                pincode: { type: 'string' }
              }
            },
            isVerified: { type: 'boolean' },
            isActive: { type: 'boolean' },
            createdAt: { type: 'string', format: 'date-time' }
          }
        },
        Vendor: {
          type: 'object',
          properties: {
            _id: { type: 'string' },
            userId: { type: 'string' },
            businessName: { type: 'string' },
            businessType: {
              type: 'string',
              enum: ['individual', 'partnership', 'company', 'cooperative']
            },
            specializations: {
              type: 'array',
              items: { type: 'string' }
            },
            description: { type: 'string' },
            rating: {
              type: 'object',
              properties: {
                average: { type: 'number' },
                count: { type: 'number' }
              }
            },
            deliveryRadius: { type: 'number' },
            minimumOrderValue: { type: 'number' },
            isActive: { type: 'boolean' },
            isVerified: { type: 'boolean' },
            createdAt: { type: 'string', format: 'date-time' }
          }
        },
        Material: {
          type: 'object',
          properties: {
            _id: { type: 'string' },
            vendorId: { type: 'string' },
            name: { type: 'string' },
            description: { type: 'string' },
            category: { type: 'string' },
            pricing: {
              type: 'object',
              properties: {
                basePrice: { type: 'number' },
                unit: { type: 'string' }
              }
            },
            inventory: {
              type: 'object',
              properties: {
                currentStock: { type: 'number' },
                lowStockThreshold: { type: 'number' }
              }
            },
            isActive: { type: 'boolean' },
            createdAt: { type: 'string', format: 'date-time' }
          }
        },
        Order: {
          type: 'object',
          properties: {
            _id: { type: 'string' },
            orderNumber: { type: 'string' },
            customerId: { type: 'string' },
            vendorId: { type: 'string' },
            items: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  materialId: { type: 'string' },
                  name: { type: 'string' },
                  quantity: { type: 'number' },
                  pricePerUnit: { type: 'number' },
                  totalPrice: { type: 'number' }
                }
              }
            },
            pricing: {
              type: 'object',
              properties: {
                subtotal: { type: 'number' },
                total: { type: 'number' }
              }
            },
            status: {
              type: 'string',
              enum: ['pending', 'confirmed', 'preparing', 'out_for_delivery', 'delivered', 'cancelled']
            },
            createdAt: { type: 'string', format: 'date-time' }
          }
        },
        Error: {
          type: 'object',
          properties: {
            success: { type: 'boolean', example: false },
            message: { type: 'string' },
            errors: {
              type: 'array',
              items: { type: 'string' }
            }
          }
        },
        Success: {
          type: 'object',
          properties: {
            success: { type: 'boolean', example: true },
            message: { type: 'string' },
            data: { type: 'object' }
          }
        }
      }
    },
    tags: [
      {
        name: 'Authentication',
        description: 'User authentication and authorization'
      },
      {
        name: 'Users',
        description: 'User management'
      },
      {
        name: 'Vendors',
        description: 'Vendor management and profiles'
      },
      {
        name: 'Materials',
        description: 'Construction materials catalog'
      },
      {
        name: 'Orders',
        description: 'Order management and tracking'
      },
      {
        name: 'Services',
        description: 'Raj Mistri services'
      },
      {
        name: 'Reviews',
        description: 'Reviews and ratings'
      },
      {
        name: 'Payments',
        description: 'Payment processing'
      },
      {
        name: 'Notifications',
        description: 'Push notifications and messaging'
      }
    ]
  },
  apis: ['./routes/*.js'], // Path to the API docs
};

const specs = swaggerJsdoc(options);

module.exports = specs;
