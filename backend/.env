# Server Configuration
PORT=3000
NODE_ENV=development

# Database
MONGODB_URI=mongodb://localhost:27017/buildbid
REDIS_URL=redis://localhost:6379

# JWT
JWT_SECRET=buildbid_super_secret_jwt_key_2024
JWT_EXPIRE=7d

# Cloudinary (Image Storage) - Demo values
CLOUDINARY_CLOUD_NAME=demo
CLOUDINARY_API_KEY=demo_key
CLOUDINARY_API_SECRET=demo_secret

# Twilio (SMS) - Demo values
TWILIO_ACCOUNT_SID=demo_sid
TWILIO_AUTH_TOKEN=demo_token
TWILIO_PHONE_NUMBER=+**********

# Email
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=demo_password

# Payment Gateway - Demo values
RAZORPAY_KEY_ID=demo_key_id
RAZORPAY_KEY_SECRET=demo_key_secret

# Firebase - Demo values
FIREBASE_PROJECT_ID=demo_project
FIREBASE_PRIVATE_KEY=demo_private_key
FIREBASE_CLIENT_EMAIL=<EMAIL>

# Google Maps
GOOGLE_MAPS_API_KEY=demo_maps_key

# App Configuration
APP_NAME=BuildBid
APP_URL=http://localhost:3000
FRONTEND_URL=http://localhost:3000

# Security
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
