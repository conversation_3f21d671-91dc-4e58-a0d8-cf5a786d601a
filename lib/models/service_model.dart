class ServiceModel {
  final String id;
  final String name;
  final String description;
  final String category;
  final List<String> subcategories;
  final double pricePerHour;
  final double pricePerDay;
  final String unit; // hour, day, project
  final List<String> imageUrls;
  final String providerId;
  final String providerName;
  final String providerPhone;
  final double providerRating;
  final int totalJobs;
  final int yearsExperience;
  final List<String> skills;
  final List<String> certifications;
  final bool isAvailable;
  final String location;
  final String city;
  final String state;
  final double radiusKm;
  final Map<String, dynamic> availability; // day-wise availability
  final List<String> languages;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isVerified;

  ServiceModel({
    required this.id,
    required this.name,
    required this.description,
    required this.category,
    required this.subcategories,
    required this.pricePerHour,
    required this.pricePerDay,
    required this.unit,
    required this.imageUrls,
    required this.providerId,
    required this.providerName,
    required this.providerPhone,
    required this.providerRating,
    required this.totalJobs,
    required this.yearsExperience,
    required this.skills,
    required this.certifications,
    required this.isAvailable,
    required this.location,
    required this.city,
    required this.state,
    required this.radiusKm,
    required this.availability,
    required this.languages,
    required this.createdAt,
    required this.updatedAt,
    this.isVerified = false,
  });

  double get effectivePrice => unit == 'hour' ? pricePerHour : pricePerDay;

  factory ServiceModel.fromMap(Map<String, dynamic> map) {
    return ServiceModel(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      category: map['category'] ?? '',
      subcategories: List<String>.from(map['subcategories'] ?? []),
      pricePerHour: (map['pricePerHour'] ?? 0.0).toDouble(),
      pricePerDay: (map['pricePerDay'] ?? 0.0).toDouble(),
      unit: map['unit'] ?? 'hour',
      imageUrls: List<String>.from(map['imageUrls'] ?? []),
      providerId: map['providerId'] ?? '',
      providerName: map['providerName'] ?? '',
      providerPhone: map['providerPhone'] ?? '',
      providerRating: (map['providerRating'] ?? 0.0).toDouble(),
      totalJobs: map['totalJobs'] ?? 0,
      yearsExperience: map['yearsExperience'] ?? 0,
      skills: List<String>.from(map['skills'] ?? []),
      certifications: List<String>.from(map['certifications'] ?? []),
      isAvailable: map['isAvailable'] ?? true,
      location: map['location'] ?? '',
      city: map['city'] ?? '',
      state: map['state'] ?? '',
      radiusKm: (map['radiusKm'] ?? 0.0).toDouble(),
      availability: Map<String, dynamic>.from(map['availability'] ?? {}),
      languages: List<String>.from(map['languages'] ?? []),
      createdAt: map['createdAt'] is String 
          ? DateTime.parse(map['createdAt']) 
          : DateTime.now(),
      updatedAt: map['updatedAt'] is String 
          ? DateTime.parse(map['updatedAt']) 
          : DateTime.now(),
      isVerified: map['isVerified'] ?? false,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'category': category,
      'subcategories': subcategories,
      'pricePerHour': pricePerHour,
      'pricePerDay': pricePerDay,
      'unit': unit,
      'imageUrls': imageUrls,
      'providerId': providerId,
      'providerName': providerName,
      'providerPhone': providerPhone,
      'providerRating': providerRating,
      'totalJobs': totalJobs,
      'yearsExperience': yearsExperience,
      'skills': skills,
      'certifications': certifications,
      'isAvailable': isAvailable,
      'location': location,
      'city': city,
      'state': state,
      'radiusKm': radiusKm,
      'availability': availability,
      'languages': languages,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'isVerified': isVerified,
    };
  }

  ServiceModel copyWith({
    String? id,
    String? name,
    String? description,
    String? category,
    List<String>? subcategories,
    double? pricePerHour,
    double? pricePerDay,
    String? unit,
    List<String>? imageUrls,
    String? providerId,
    String? providerName,
    String? providerPhone,
    double? providerRating,
    int? totalJobs,
    int? yearsExperience,
    List<String>? skills,
    List<String>? certifications,
    bool? isAvailable,
    String? location,
    String? city,
    String? state,
    double? radiusKm,
    Map<String, dynamic>? availability,
    List<String>? languages,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isVerified,
  }) {
    return ServiceModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      category: category ?? this.category,
      subcategories: subcategories ?? this.subcategories,
      pricePerHour: pricePerHour ?? this.pricePerHour,
      pricePerDay: pricePerDay ?? this.pricePerDay,
      unit: unit ?? this.unit,
      imageUrls: imageUrls ?? this.imageUrls,
      providerId: providerId ?? this.providerId,
      providerName: providerName ?? this.providerName,
      providerPhone: providerPhone ?? this.providerPhone,
      providerRating: providerRating ?? this.providerRating,
      totalJobs: totalJobs ?? this.totalJobs,
      yearsExperience: yearsExperience ?? this.yearsExperience,
      skills: skills ?? this.skills,
      certifications: certifications ?? this.certifications,
      isAvailable: isAvailable ?? this.isAvailable,
      location: location ?? this.location,
      city: city ?? this.city,
      state: state ?? this.state,
      radiusKm: radiusKm ?? this.radiusKm,
      availability: availability ?? this.availability,
      languages: languages ?? this.languages,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isVerified: isVerified ?? this.isVerified,
    );
  }

  @override
  String toString() {
    return 'ServiceModel(id: $id, name: $name, providerName: $providerName)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ServiceModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

class ServiceBookingModel {
  final String id;
  final String serviceId;
  final String serviceName;
  final String providerId;
  final String providerName;
  final String providerPhone;
  final String customerId;
  final String customerName;
  final String customerPhone;
  final DateTime bookingDate;
  final String timeSlot;
  final int numberOfDays;
  final double totalAmount;
  final String workAddress;
  final String city;
  final String state;
  final String pincode;
  final String status; // pending, confirmed, in_progress, completed, cancelled
  final String paymentStatus; // pending, paid, refunded
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? cancellationReason;
  final double? rating;
  final String? review;

  ServiceBookingModel({
    required this.id,
    required this.serviceId,
    required this.serviceName,
    required this.providerId,
    required this.providerName,
    required this.providerPhone,
    required this.customerId,
    required this.customerName,
    required this.customerPhone,
    required this.bookingDate,
    required this.timeSlot,
    required this.numberOfDays,
    required this.totalAmount,
    required this.workAddress,
    required this.city,
    required this.state,
    required this.pincode,
    required this.status,
    required this.paymentStatus,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
    this.cancellationReason,
    this.rating,
    this.review,
  });

  factory ServiceBookingModel.fromMap(Map<String, dynamic> map) {
    return ServiceBookingModel(
      id: map['id'] ?? '',
      serviceId: map['serviceId'] ?? '',
      serviceName: map['serviceName'] ?? '',
      providerId: map['providerId'] ?? '',
      providerName: map['providerName'] ?? '',
      providerPhone: map['providerPhone'] ?? '',
      customerId: map['customerId'] ?? '',
      customerName: map['customerName'] ?? '',
      customerPhone: map['customerPhone'] ?? '',
      bookingDate: map['bookingDate'] is String 
          ? DateTime.parse(map['bookingDate']) 
          : DateTime.now(),
      timeSlot: map['timeSlot'] ?? '',
      numberOfDays: map['numberOfDays'] ?? 1,
      totalAmount: (map['totalAmount'] ?? 0.0).toDouble(),
      workAddress: map['workAddress'] ?? '',
      city: map['city'] ?? '',
      state: map['state'] ?? '',
      pincode: map['pincode'] ?? '',
      status: map['status'] ?? 'pending',
      paymentStatus: map['paymentStatus'] ?? 'pending',
      notes: map['notes'],
      createdAt: map['createdAt'] is String 
          ? DateTime.parse(map['createdAt']) 
          : DateTime.now(),
      updatedAt: map['updatedAt'] is String 
          ? DateTime.parse(map['updatedAt']) 
          : DateTime.now(),
      cancellationReason: map['cancellationReason'],
      rating: map['rating']?.toDouble(),
      review: map['review'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'serviceId': serviceId,
      'serviceName': serviceName,
      'providerId': providerId,
      'providerName': providerName,
      'providerPhone': providerPhone,
      'customerId': customerId,
      'customerName': customerName,
      'customerPhone': customerPhone,
      'bookingDate': bookingDate.toIso8601String(),
      'timeSlot': timeSlot,
      'numberOfDays': numberOfDays,
      'totalAmount': totalAmount,
      'workAddress': workAddress,
      'city': city,
      'state': state,
      'pincode': pincode,
      'status': status,
      'paymentStatus': paymentStatus,
      'notes': notes,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'cancellationReason': cancellationReason,
      'rating': rating,
      'review': review,
    };
  }
}
