import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../utils/app_colors.dart';
import 'vendor_login_screen.dart';
import 'vendor_home_screen.dart';

class VendorApp extends StatelessWidget {
  const VendorApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        // Check if user is authenticated as vendor
        if (authProvider.isAuthenticated && 
            authProvider.currentUser != null &&
            authProvider.currentUser!.role == 'vendor') {
          return const VendorHomeScreen();
        }
        
        return const VendorLoginScreen();
      },
    );
  }
}
