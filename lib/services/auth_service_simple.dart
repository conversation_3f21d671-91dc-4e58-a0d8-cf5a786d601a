import '../models/user_model.dart';
import '../utils/constants.dart';

class AuthService {
  // Mock user for demo
  UserModel? _currentUser;

  // Get current user
  UserModel? get currentUser => _currentUser;

  // Mock auth state stream
  Stream<UserModel?> get authStateChanges async* {
    yield _currentUser;
  }

  // Send OTP to phone number (mock)
  Future<void> sendOTP({
    required String phoneNumber,
    required Function(String) verificationCompleted,
    required Function(String) verificationFailed,
    required Function(String, int?) codeSent,
    required Function(String) codeAutoRetrievalTimeout,
  }) async {
    // Simulate network delay
    await Future.delayed(const Duration(seconds: 2));
    
    // Mock verification ID
    codeSent('mock_verification_id', null);
  }

  // Verify OTP and sign in (mock)
  Future<bool> verifyOTP({
    required String verificationId,
    required String smsCode,
  }) async {
    // Simulate network delay
    await Future.delayed(const Duration(seconds: 1));
    
    // Accept any 6-digit code for demo
    if (smsCode.length == 6) {
      return true;
    }
    
    throw Exception('Invalid OTP');
  }

  // Create user profile
  Future<UserModel> createUserProfile({
    required String name,
    required String userType,
    required String phoneNumber,
    String? email,
    String? address,
    String? city,
    String? state,
    String? pincode,
  }) async {
    // Simulate network delay
    await Future.delayed(const Duration(seconds: 1));

    final userModel = UserModel(
      id: 'mock_user_id',
      uid: 'mock_user_id',
      phone: phoneNumber,
      name: name,
      role: userType,
      email: email ?? '',
      status: 'active',
      location: {
        'address': address,
        'city': city,
        'state': state,
        'pincode': pincode,
      },
      createdAt: DateTime.now(),
    );

    _currentUser = userModel;
    return userModel;
  }

  // Get user profile
  Future<UserModel?> getUserProfile(String userId) async {
    await Future.delayed(const Duration(milliseconds: 500));
    return _currentUser;
  }

  // Update user profile
  Future<void> updateUserProfile(UserModel userModel) async {
    await Future.delayed(const Duration(seconds: 1));
    _currentUser = userModel;
  }

  // Check if user profile exists
  Future<bool> userProfileExists(String userId) async {
    await Future.delayed(const Duration(milliseconds: 500));
    return _currentUser != null;
  }

  // Sign out
  Future<void> signOut() async {
    await Future.delayed(const Duration(milliseconds: 500));
    _currentUser = null;
  }

  // Delete user account
  Future<void> deleteAccount() async {
    await Future.delayed(const Duration(seconds: 1));
    _currentUser = null;
  }

  // Get users by type (mock)
  Future<List<UserModel>> getUsersByType(String userType) async {
    await Future.delayed(const Duration(seconds: 1));
    return [];
  }

  // Search users (mock)
  Future<List<UserModel>> searchUsers(String query) async {
    await Future.delayed(const Duration(seconds: 1));
    return [];
  }
}
