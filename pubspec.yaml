name: buildbid
description: A Flutter app for construction material bidding and ordering
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter

  # Firebase Backend (commented out for demo)
  # firebase_core: ^2.10.0
  # cloud_firestore: ^4.5.2
  # firebase_auth: ^4.4.2
  # firebase_messaging: ^14.4.1
  # firebase_storage: ^11.1.1

  # State Management
  provider: ^6.1.1

  # UI Components & Animations
  cupertino_icons: ^1.0.6
  lottie: ^2.7.0
  country_code_picker: ^3.0.0
  sms_autofill: ^2.3.0
  cached_network_image: ^3.3.0
  photo_view: ^0.14.0
  carousel_slider: ^4.2.1
  shimmer: ^3.0.0
  flutter_staggered_grid_view: ^0.7.0

  # Charts & Data Visualization
  fl_chart: ^0.66.0

  # Utilities
  shared_preferences: ^2.2.2
  intl: ^0.20.2
  http: ^1.1.2
  image_picker: ^1.0.4
  permission_handler: ^11.0.1
  geolocator: ^10.1.0
  url_launcher: ^6.2.1
  path_provider: ^2.1.1

  # Payments
  razorpay_flutter: ^1.3.6

  # Localization
  flutter_localizations:
    sdk: flutter

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

flutter:
  uses-material-design: true
