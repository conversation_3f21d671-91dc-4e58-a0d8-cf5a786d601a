import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';

class RealtimeNotificationService {
  static final RealtimeNotificationService _instance = RealtimeNotificationService._internal();
  factory RealtimeNotificationService() => _instance;
  RealtimeNotificationService._internal();

  final StreamController<Map<String, dynamic>> _notificationController = 
      StreamController<Map<String, dynamic>>.broadcast();

  Stream<Map<String, dynamic>> get notificationStream => _notificationController.stream;

  // Send real-time notification
  Future<void> sendRealtimeNotification({
    required String userId,
    required String title,
    required String body,
    Map<String, dynamic>? data,
  }) async {
    try {
      final notification = {
        'userId': userId,
        'title': title,
        'body': body,
        'data': data ?? {},
        'timestamp': DateTime.now().toIso8601String(),
        'id': DateTime.now().millisecondsSinceEpoch.toString(),
      };

      // Emit to stream for real-time updates
      _notificationController.add(notification);

      if (kDebugMode) {
        print('🔔 Real-time notification sent to $userId:');
        print('   Title: $title');
        print('   Body: $body');
        if (data != null && data.isNotEmpty) {
          print('   Data: ${jsonEncode(data)}');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to send real-time notification: $e');
      }
    }
  }

  // Send order notification
  Future<void> sendOrderNotification({
    required String userId,
    required String orderId,
    required String type,
    Map<String, dynamic>? additionalData,
  }) async {
    String title = '';
    String body = '';
    
    switch (type) {
      case 'new_order':
        title = 'New Order Received! 🛒';
        body = 'You have a new order #${orderId.substring(orderId.length - 6)}';
        break;
      case 'order_confirmed':
        title = 'Order Confirmed! ✅';
        body = 'Your order #${orderId.substring(orderId.length - 6)} has been confirmed';
        break;
      case 'order_preparing':
        title = 'Order Being Prepared 👨‍🍳';
        body = 'Your order #${orderId.substring(orderId.length - 6)} is being prepared';
        break;
      case 'order_shipped':
        title = 'Order Shipped! 🚚';
        body = 'Your order #${orderId.substring(orderId.length - 6)} is on the way';
        break;
      case 'order_delivered':
        title = 'Order Delivered! 📦';
        body = 'Your order #${orderId.substring(orderId.length - 6)} has been delivered';
        break;
    }

    final data = {
      'orderId': orderId,
      'type': type,
      ...?additionalData,
    };

    await sendRealtimeNotification(
      userId: userId,
      title: title,
      body: body,
      data: data,
    );
  }

  // Cleanup
  void dispose() {
    _notificationController.close();
  }
}
