import 'package:flutter/material.dart';

// Common screens
import '../screens/common/splash_screen.dart';
import '../screens/common/role_selection_screen.dart';
import '../screens/common/login_screen.dart';
import '../screens/common/profile_setup_screen.dart';
import '../screens/common/profile_screen.dart';

// Builder screens
import '../screens/builder/builder_dashboard.dart';
import '../screens/builder/post_requirement_screen.dart';
import '../screens/builder/my_requirements_screen.dart';
import '../screens/builder/view_bids_screen.dart';
import '../screens/builder/live_bids_screen.dart';
import '../screens/builder/track_bulk_order_screen.dart';

// Mistri screens
import '../screens/mistri/mistri_dashboard.dart';
import '../screens/mistri/home_screen.dart';
import '../screens/mistri/product_list_screen.dart';
import '../screens/mistri/cart_screen.dart';
import '../screens/mistri/instant_order_screen.dart';
import '../screens/mistri/order_tracking_screen.dart';

// Vendor screens
import '../screens/vendor/vendor_dashboard.dart';
import '../screens/vendor/inventory_management_screen.dart';
import '../screens/vendor/respond_to_bids_screen.dart';
import '../screens/vendor/instant_order_requests_screen.dart';
import '../screens/vendor/earnings_screen.dart';

class AppRoutes {
  // Route names
  static const String splash = '/';
  static const String roleSelection = '/role-selection';
  static const String login = '/login';
  static const String profileSetup = '/profile-setup';
  static const String profile = '/profile';

  // Builder routes
  static const String builderDashboard = '/builder-dashboard';
  static const String postRequirement = '/post-requirement';
  static const String myRequirements = '/my-requirements';
  static const String viewBids = '/view-bids';
  static const String liveBids = '/live-bids';
  static const String trackBulkOrder = '/track-bulk-order';

  // Mistri routes
  static const String mistriDashboard = '/mistri-dashboard';
  static const String mistriHome = '/mistri-home';
  static const String productList = '/product-list';
  static const String cart = '/cart';
  static const String instantOrder = '/instant-order';
  static const String orderTracking = '/order-tracking';

  // Vendor routes
  static const String vendorDashboard = '/vendor-dashboard';
  static const String inventoryManagement = '/inventory-management';
  static const String respondToBids = '/respond-to-bids';
  static const String instantOrderRequests = '/instant-order-requests';
  static const String earnings = '/earnings';

  // Route generator
  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case splash:
        return MaterialPageRoute(builder: (_) => const SplashScreen());
      
      case roleSelection:
        return MaterialPageRoute(builder: (_) => const RoleSelectionScreen());
      
      case login:
        final args = settings.arguments as Map<String, dynamic>?;
        return MaterialPageRoute(
          builder: (_) => LoginScreen(
            userRole: args?['userRole'] ?? 'builder',
          ),
        );
      
      case profileSetup:
        final args = settings.arguments as Map<String, dynamic>?;
        return MaterialPageRoute(
          builder: (_) => ProfileSetupScreen(
            phoneNumber: args?['phoneNumber'] ?? '',
            userRole: args?['userRole'] ?? 'builder',
          ),
        );
      
      case profile:
        return MaterialPageRoute(builder: (_) => const ProfileScreen());

      // Builder routes
      case builderDashboard:
        return MaterialPageRoute(builder: (_) => const BuilderDashboard());
      
      case postRequirement:
        return MaterialPageRoute(builder: (_) => const PostRequirementScreen());
      
      case myRequirements:
        return MaterialPageRoute(builder: (_) => const MyRequirementsScreen());
      
      case viewBids:
        final args = settings.arguments as Map<String, dynamic>?;
        return MaterialPageRoute(
          builder: (_) => ViewBidsScreen(
            requirementId: args?['requirementId'] ?? '',
          ),
        );
      
      case liveBids:
        return MaterialPageRoute(builder: (_) => const LiveBidsScreen());
      
      case trackBulkOrder:
        final args = settings.arguments as Map<String, dynamic>?;
        return MaterialPageRoute(
          builder: (_) => TrackBulkOrderScreen(
            orderId: args?['orderId'] ?? '',
          ),
        );

      // Mistri routes
      case mistriDashboard:
        return MaterialPageRoute(builder: (_) => const MistriDashboard());
      
      case mistriHome:
        return MaterialPageRoute(builder: (_) => const HomeScreen());
      
      case productList:
        final args = settings.arguments as Map<String, dynamic>?;
        return MaterialPageRoute(
          builder: (_) => ProductListScreen(
            category: args?['category'] ?? 'all',
          ),
        );
      
      case cart:
        return MaterialPageRoute(builder: (_) => const CartScreen());
      
      case instantOrder:
        final args = settings.arguments as Map<String, dynamic>?;
        return MaterialPageRoute(
          builder: (_) => InstantOrderScreen(
            productId: args?['productId'] ?? '',
          ),
        );
      
      case orderTracking:
        final args = settings.arguments as Map<String, dynamic>?;
        return MaterialPageRoute(
          builder: (_) => OrderTrackingScreen(
            orderId: args?['orderId'] ?? '',
          ),
        );

      // Vendor routes
      case vendorDashboard:
        return MaterialPageRoute(builder: (_) => const VendorDashboard());
      
      case inventoryManagement:
        return MaterialPageRoute(builder: (_) => const InventoryManagementScreen());
      
      case respondToBids:
        final args = settings.arguments as Map<String, dynamic>?;
        return MaterialPageRoute(
          builder: (_) => RespondToBidsScreen(
            requirementId: args?['requirementId'] ?? '',
          ),
        );
      
      case instantOrderRequests:
        return MaterialPageRoute(builder: (_) => const InstantOrderRequestsScreen());
      
      case earnings:
        return MaterialPageRoute(builder: (_) => const EarningsScreen());

      default:
        return MaterialPageRoute(
          builder: (_) => Scaffold(
            appBar: AppBar(title: const Text('Page Not Found')),
            body: const Center(
              child: Text('Page not found!'),
            ),
          ),
        );
    }
  }

  // Helper method to get dashboard route based on user role
  static String getDashboardRoute(String userRole) {
    switch (userRole.toLowerCase()) {
      case 'builder':
        return builderDashboard;
      case 'mistri':
      case 'raj mistri':
        return mistriDashboard;
      case 'vendor':
        return vendorDashboard;
      default:
        return roleSelection;
    }
  }

  // Helper method to navigate to dashboard based on user role
  static void navigateToDashboard(BuildContext context, String userRole) {
    final route = getDashboardRoute(userRole);
    Navigator.pushNamedAndRemoveUntil(
      context,
      route,
      (route) => false,
    );
  }
}
