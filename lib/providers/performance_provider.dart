import 'package:flutter/material.dart';
import 'package:firebase_performance/firebase_performance.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import '../services/performance_service.dart';

class PerformanceProvider extends ChangeNotifier {
  final PerformanceService _performanceService = PerformanceService();
  final FirebasePerformance _performance = FirebasePerformance.instance;
  final FirebaseCrashlytics _crashlytics = FirebaseCrashlytics.instance;

  // Performance metrics
  Map<String, dynamic> _performanceMetrics = {};
  Map<String, Trace> _activeTraces = {};
  Map<String, HttpMetric> _activeHttpMetrics = {};

  Map<String, dynamic> get performanceMetrics => _performanceMetrics;

  // Start performance trace for critical operations
  Future<void> startTrace(String traceName) async {
    try {
      final trace = _performance.newTrace(traceName);
      await trace.start();
      _activeTraces[traceName] = trace;
      
      _performanceMetrics['${traceName}_started'] = DateTime.now().millisecondsSinceEpoch;
      notifyListeners();
    } catch (e) {
      _crashlytics.recordError(e, null, fatal: false);
    }
  }

  // Stop performance trace
  Future<void> stopTrace(String traceName, {Map<String, String>? attributes}) async {
    try {
      final trace = _activeTraces[traceName];
      if (trace != null) {
        // Add custom attributes
        if (attributes != null) {
          attributes.forEach((key, value) {
            trace.putAttribute(key, value);
          });
        }
        
        await trace.stop();
        _activeTraces.remove(traceName);
        
        final endTime = DateTime.now().millisecondsSinceEpoch;
        final startTime = _performanceMetrics['${traceName}_started'] ?? endTime;
        final duration = endTime - startTime;
        
        _performanceMetrics['${traceName}_duration'] = duration;
        _performanceMetrics['${traceName}_completed'] = endTime;
        
        notifyListeners();
      }
    } catch (e) {
      _crashlytics.recordError(e, null, fatal: false);
    }
  }

  // Track HTTP requests performance
  Future<void> startHttpMetric(String url, String httpMethod) async {
    try {
      final httpMetric = _performance.newHttpMetric(url, HttpMethod.values.firstWhere(
        (method) => method.toString().split('.').last.toUpperCase() == httpMethod.toUpperCase(),
        orElse: () => HttpMethod.Get,
      ));
      
      await httpMetric.start();
      _activeHttpMetrics[url] = httpMetric;
      
      _performanceMetrics['http_${url}_started'] = DateTime.now().millisecondsSinceEpoch;
      notifyListeners();
    } catch (e) {
      _crashlytics.recordError(e, null, fatal: false);
    }
  }

  // Stop HTTP metric
  Future<void> stopHttpMetric(String url, {
    int? responseCode,
    int? requestPayloadSize,
    int? responsePayloadSize,
    String? contentType,
  }) async {
    try {
      final httpMetric = _activeHttpMetrics[url];
      if (httpMetric != null) {
        if (responseCode != null) {
          httpMetric.responseCode = responseCode;
        }
        if (requestPayloadSize != null) {
          httpMetric.requestPayloadSize = requestPayloadSize;
        }
        if (responsePayloadSize != null) {
          httpMetric.responsePayloadSize = responsePayloadSize;
        }
        if (contentType != null) {
          httpMetric.responseContentType = contentType;
        }
        
        await httpMetric.stop();
        _activeHttpMetrics.remove(url);
        
        final endTime = DateTime.now().millisecondsSinceEpoch;
        final startTime = _performanceMetrics['http_${url}_started'] ?? endTime;
        final duration = endTime - startTime;
        
        _performanceMetrics['http_${url}_duration'] = duration;
        _performanceMetrics['http_${url}_response_code'] = responseCode;
        
        notifyListeners();
      }
    } catch (e) {
      _crashlytics.recordError(e, null, fatal: false);
    }
  }

  // Track app startup performance
  Future<void> trackAppStartup() async {
    try {
      await startTrace('app_startup');
      
      // Add custom attributes for startup
      await Future.delayed(Duration(milliseconds: 100)); // Simulate startup time
      
      await stopTrace('app_startup', attributes: {
        'startup_type': 'cold_start',
        'app_version': '1.0.0',
        'platform': 'flutter',
      });
    } catch (e) {
      _crashlytics.recordError(e, null, fatal: false);
    }
  }

  // Track screen loading performance
  Future<void> trackScreenLoad(String screenName) async {
    try {
      final traceName = 'screen_load_$screenName';
      await startTrace(traceName);
      
      // Screen load completed - call this when screen is fully loaded
      await stopTrace(traceName, attributes: {
        'screen_name': screenName,
        'load_type': 'initial',
      });
    } catch (e) {
      _crashlytics.recordError(e, null, fatal: false);
    }
  }

  // Track database operations performance
  Future<void> trackDatabaseOperation(String operation, String collection) async {
    try {
      final traceName = 'db_${operation}_$collection';
      await startTrace(traceName);
      
      // Database operation completed
      await stopTrace(traceName, attributes: {
        'operation': operation,
        'collection': collection,
        'database': 'firestore',
      });
    } catch (e) {
      _crashlytics.recordError(e, null, fatal: false);
    }
  }

  // Track API call performance
  Future<void> trackApiCall(String endpoint, String method) async {
    try {
      await startHttpMetric(endpoint, method);
      
      // API call completed - call stopHttpMetric when done
    } catch (e) {
      _crashlytics.recordError(e, null, fatal: false);
    }
  }

  // Track memory usage
  void trackMemoryUsage(String context) {
    try {
      _performanceMetrics['memory_${context}_timestamp'] = DateTime.now().millisecondsSinceEpoch;
      _performanceMetrics['memory_${context}_context'] = context;
      notifyListeners();
    } catch (e) {
      _crashlytics.recordError(e, null, fatal: false);
    }
  }

  // Track frame rendering performance
  void trackFrameMetrics(String screenName, Duration frameDuration) {
    try {
      _performanceMetrics['frame_${screenName}_duration'] = frameDuration.inMilliseconds;
      _performanceMetrics['frame_${screenName}_timestamp'] = DateTime.now().millisecondsSinceEpoch;
      
      // Alert if frame time is too high (>16ms for 60fps)
      if (frameDuration.inMilliseconds > 16) {
        _performanceMetrics['frame_${screenName}_slow'] = true;
      }
      
      notifyListeners();
    } catch (e) {
      _crashlytics.recordError(e, null, fatal: false);
    }
  }

  // Get performance summary
  Map<String, dynamic> getPerformanceSummary() {
    return {
      'metrics': _performanceMetrics,
      'active_traces': _activeTraces.keys.toList(),
      'active_http_metrics': _activeHttpMetrics.keys.toList(),
      'last_updated': DateTime.now().toIso8601String(),
    };
  }

  // Clean up resources
  void cleanup() {
    _activeTraces.clear();
    _activeHttpMetrics.clear();
    _performanceMetrics.clear();
    notifyListeners();
  }
}
