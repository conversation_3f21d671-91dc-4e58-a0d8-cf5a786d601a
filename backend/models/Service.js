const mongoose = require('mongoose');

const serviceSchema = new mongoose.Schema({
  // Service Provider Information
  providerId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  
  // Service Details
  name: {
    type: String,
    required: [true, 'Service name is required'],
    trim: true,
    maxlength: [200, 'Service name cannot exceed 200 characters']
  },
  description: {
    type: String,
    required: [true, 'Service description is required'],
    maxlength: [1000, 'Description cannot exceed 1000 characters']
  },
  
  // Service Category
  category: {
    type: String,
    required: [true, 'Service category is required'],
    enum: [
      'masonry',      // Brickwork, plastering, foundation
      'plumbing',     // Pipe fitting, repairs, installation
      'electrical',   // Wiring, repairs, installation
      'painting',     // Wall painting, texture work
      'tiling',       // Floor/wall tiling, marble work
      'carpentry',    // Furniture, doors, windows
      'roofing',      // Roof installation, repairs
      'flooring',     // Floor installation, polishing
      'waterproofing', // Waterproofing solutions
      'renovation',   // General renovation work
      'other'
    ]
  },
  subcategory: {
    type: String,
    trim: true
  },
  
  // Skills and Expertise
  skills: [{
    type: String,
    trim: true
  }],
  specializations: [{
    type: String,
    trim: true
  }],
  
  // Experience
  experience: {
    years: {
      type: Number,
      required: [true, 'Years of experience is required'],
      min: [0, 'Experience cannot be negative'],
      max: [50, 'Experience cannot exceed 50 years']
    },
    description: String,
    previousWork: [{
      projectName: String,
      description: String,
      duration: String,
      clientName: String,
      images: [String],
      completedAt: Date
    }]
  },
  
  // Pricing
  pricing: {
    hourlyRate: {
      type: Number,
      required: [true, 'Hourly rate is required'],
      min: [0, 'Hourly rate cannot be negative']
    },
    dailyRate: {
      type: Number,
      required: [true, 'Daily rate is required'],
      min: [0, 'Daily rate cannot be negative']
    },
    minimumHours: {
      type: Number,
      default: 4,
      min: [1, 'Minimum hours must be at least 1']
    },
    advancePayment: {
      required: { type: Boolean, default: false },
      percentage: { type: Number, min: 0, max: 100 }
    },
    materialCharges: {
      included: { type: Boolean, default: false },
      rate: Number // per unit or percentage
    }
  },
  
  // Availability
  availability: {
    isAvailable: {
      type: Boolean,
      default: true
    },
    workingDays: [{
      type: String,
      enum: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
    }],
    workingHours: {
      start: String, // HH:MM format
      end: String    // HH:MM format
    },
    unavailableDates: [{
      from: Date,
      to: Date,
      reason: String
    }],
    maxBookingsPerDay: {
      type: Number,
      default: 1,
      min: [1, 'Maximum bookings per day must be at least 1']
    }
  },
  
  // Service Area
  serviceArea: {
    radius: {
      type: Number,
      default: 15,
      min: [1, 'Service radius must be at least 1 km'],
      max: [100, 'Service radius cannot exceed 100 km']
    },
    areas: [String], // Array of area names
    travelCharges: {
      freeRadius: { type: Number, default: 5 }, // km
      chargePerKm: { type: Number, default: 10 }
    }
  },
  
  // Certifications and Documents
  certifications: [{
    name: String,
    issuedBy: String,
    issuedDate: Date,
    validUntil: Date,
    certificateNumber: String,
    document: {
      url: String,
      publicId: String
    },
    verified: { type: Boolean, default: false }
  }],
  
  // Tools and Equipment
  tools: [{
    name: String,
    owned: { type: Boolean, default: true },
    condition: {
      type: String,
      enum: ['excellent', 'good', 'fair', 'needs_replacement']
    }
  }],
  
  // Performance Metrics
  metrics: {
    totalJobs: { type: Number, default: 0 },
    completedJobs: { type: Number, default: 0 },
    cancelledJobs: { type: Number, default: 0 },
    rating: {
      overall: { type: Number, default: 0, min: 0, max: 5 },
      quality: { type: Number, default: 0, min: 0, max: 5 },
      punctuality: { type: Number, default: 0, min: 0, max: 5 },
      behavior: { type: Number, default: 0, min: 0, max: 5 },
      count: { type: Number, default: 0 }
    },
    averageCompletionTime: Number, // in hours
    responseTime: Number, // average response time in minutes
    repeatCustomers: { type: Number, default: 0 }
  },
  
  // Images and Portfolio
  images: [{
    url: String,
    publicId: String,
    caption: String,
    type: {
      type: String,
      enum: ['profile', 'work_sample', 'certificate', 'tool']
    },
    isPrimary: { type: Boolean, default: false }
  }],
  
  // Reviews and Testimonials
  testimonials: [{
    customerName: String,
    review: String,
    rating: { type: Number, min: 1, max: 5 },
    projectType: String,
    date: { type: Date, default: Date.now },
    verified: { type: Boolean, default: false }
  }],
  
  // Verification Status
  verification: {
    identity: { type: Boolean, default: false },
    address: { type: Boolean, default: false },
    skills: { type: Boolean, default: false },
    background: { type: Boolean, default: false },
    verifiedAt: Date,
    verifiedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  },
  
  // Status
  isActive: {
    type: Boolean,
    default: true
  },
  isApproved: {
    type: Boolean,
    default: false
  },
  isFeatured: {
    type: Boolean,
    default: false
  },
  
  // Timestamps
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
serviceSchema.index({ providerId: 1 });
serviceSchema.index({ category: 1 });
serviceSchema.index({ 'metrics.rating.overall': -1 });
serviceSchema.index({ 'pricing.hourlyRate': 1 });
serviceSchema.index({ 'availability.isAvailable': 1 });
serviceSchema.index({ isActive: 1, isApproved: 1 });
serviceSchema.index({ createdAt: -1 });

// Virtual for completion rate
serviceSchema.virtual('completionRate').get(function() {
  if (this.metrics.totalJobs === 0) return 0;
  return Math.round((this.metrics.completedJobs / this.metrics.totalJobs) * 100);
});

// Virtual for cancellation rate
serviceSchema.virtual('cancellationRate').get(function() {
  if (this.metrics.totalJobs === 0) return 0;
  return Math.round((this.metrics.cancelledJobs / this.metrics.totalJobs) * 100);
});

// Virtual for primary image
serviceSchema.virtual('primaryImage').get(function() {
  const primary = this.images.find(img => img.isPrimary);
  return primary || this.images.find(img => img.type === 'profile') || this.images[0] || null;
});

// Virtual for verification status
serviceSchema.virtual('verificationStatus').get(function() {
  const { identity, address, skills, background } = this.verification;
  const verified = [identity, address, skills, background].filter(Boolean).length;
  return {
    percentage: (verified / 4) * 100,
    completed: verified,
    total: 4,
    isFullyVerified: verified === 4
  };
});

// Method to check availability for a date
serviceSchema.methods.isAvailableOn = function(date) {
  const dayName = date.toLocaleDateString('en-US', { weekday: 'lowercase' });
  
  // Check if working on this day
  if (!this.availability.workingDays.includes(dayName)) {
    return false;
  }
  
  // Check unavailable dates
  const isUnavailable = this.availability.unavailableDates.some(period => {
    return date >= period.from && date <= period.to;
  });
  
  return !isUnavailable && this.availability.isAvailable;
};

// Method to calculate travel charges
serviceSchema.methods.calculateTravelCharges = function(distanceKm) {
  if (distanceKm <= this.serviceArea.travelCharges.freeRadius) {
    return 0;
  }
  
  const chargeableDistance = distanceKm - this.serviceArea.travelCharges.freeRadius;
  return chargeableDistance * this.serviceArea.travelCharges.chargePerKm;
};

// Method to update rating
serviceSchema.methods.updateRating = function(ratings) {
  const { overall, quality, punctuality, behavior } = ratings;
  const count = this.metrics.rating.count;
  
  // Update individual ratings
  this.metrics.rating.overall = ((this.metrics.rating.overall * count) + overall) / (count + 1);
  this.metrics.rating.quality = ((this.metrics.rating.quality * count) + quality) / (count + 1);
  this.metrics.rating.punctuality = ((this.metrics.rating.punctuality * count) + punctuality) / (count + 1);
  this.metrics.rating.behavior = ((this.metrics.rating.behavior * count) + behavior) / (count + 1);
  this.metrics.rating.count += 1;
  
  return this.save();
};

// Static method to find services by category
serviceSchema.statics.findByCategory = function(category, options = {}) {
  const query = {
    category,
    isActive: true,
    isApproved: true,
    'availability.isAvailable': true
  };
  
  return this.find(query)
    .populate('providerId', 'name phone location profileImage')
    .sort(options.sort || { 'metrics.rating.overall': -1 })
    .limit(options.limit || 20);
};

// Static method to find nearby services
serviceSchema.statics.findNearby = function(longitude, latitude, radiusInKm = 15, options = {}) {
  return this.aggregate([
    {
      $lookup: {
        from: 'users',
        localField: 'providerId',
        foreignField: '_id',
        as: 'provider'
      }
    },
    {
      $unwind: '$provider'
    },
    {
      $match: {
        'provider.location.coordinates': {
          $near: {
            $geometry: {
              type: 'Point',
              coordinates: [longitude, latitude]
            },
            $maxDistance: radiusInKm * 1000
          }
        },
        isActive: true,
        isApproved: true,
        'availability.isAvailable': true
      }
    },
    {
      $addFields: {
        distance: {
          $divide: [
            {
              $sqrt: {
                $sum: [
                  {
                    $pow: [
                      { $subtract: [{ $arrayElemAt: ['$provider.location.coordinates', 0] }, longitude] },
                      2
                    ]
                  },
                  {
                    $pow: [
                      { $subtract: [{ $arrayElemAt: ['$provider.location.coordinates', 1] }, latitude] },
                      2
                    ]
                  }
                ]
              }
            },
            111.32
          ]
        }
      }
    },
    {
      $sort: options.sort || { distance: 1, 'metrics.rating.overall': -1 }
    },
    {
      $limit: options.limit || 20
    }
  ]);
};

module.exports = mongoose.model('Service', serviceSchema);
