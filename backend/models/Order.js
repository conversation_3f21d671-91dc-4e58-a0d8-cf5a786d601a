const mongoose = require('mongoose');

const orderSchema = new mongoose.Schema({
  // Order Identification
  orderNumber: {
    type: String,
    unique: true,
    required: true
  },
  
  // Customer Information
  customerId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  
  // Vendor Information (for single vendor orders)
  vendorId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Vendor',
    required: true
  },
  
  // Order Items
  items: [{
    materialId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Material',
      required: true
    },
    name: String, // Snapshot of material name
    quantity: {
      type: Number,
      required: true,
      min: [1, 'Quantity must be at least 1']
    },
    unit: String,
    pricePerUnit: {
      type: Number,
      required: true,
      min: [0, 'Price cannot be negative']
    },
    totalPrice: {
      type: Number,
      required: true,
      min: [0, 'Total price cannot be negative']
    },
    specifications: mongoose.Schema.Types.Mixed,
    images: [String] // Snapshot of material images
  }],
  
  // Pricing Breakdown
  pricing: {
    subtotal: {
      type: Number,
      required: true,
      min: [0, 'Subtotal cannot be negative']
    },
    tax: {
      gst: { type: Number, default: 0 },
      other: { type: Number, default: 0 },
      total: { type: Number, default: 0 }
    },
    delivery: {
      charges: { type: Number, default: 0 },
      freeDelivery: { type: Boolean, default: false }
    },
    packaging: {
      charges: { type: Number, default: 0 }
    },
    discount: {
      amount: { type: Number, default: 0 },
      code: String,
      type: { type: String, enum: ['percentage', 'fixed'] }
    },
    total: {
      type: Number,
      required: true,
      min: [0, 'Total cannot be negative']
    }
  },
  
  // Delivery Information
  delivery: {
    address: {
      street: String,
      landmark: String,
      city: String,
      state: String,
      pincode: String,
      coordinates: {
        type: [Number], // [longitude, latitude]
        index: '2dsphere'
      }
    },
    contactPerson: {
      name: String,
      phone: String
    },
    timeSlot: {
      date: Date,
      slot: {
        type: String,
        enum: ['morning', 'afternoon', 'evening', 'anytime']
      },
      startTime: String, // HH:MM format
      endTime: String    // HH:MM format
    },
    instructions: String,
    estimatedDeliveryTime: Date,
    actualDeliveryTime: Date,
    deliveryPerson: {
      name: String,
      phone: String,
      vehicleNumber: String
    },
    otp: {
      code: String,
      verified: { type: Boolean, default: false }
    }
  },
  
  // Order Status and Tracking
  status: {
    type: String,
    enum: [
      'pending',           // Order placed, awaiting vendor confirmation
      'confirmed',         // Vendor confirmed the order
      'preparing',         // Materials being prepared
      'ready_for_pickup',  // Ready for delivery
      'out_for_delivery',  // On the way to customer
      'delivered',         // Successfully delivered
      'cancelled',         // Order cancelled
      'returned',          // Order returned
      'refunded'           // Order refunded
    ],
    default: 'pending'
  },
  
  // Status History
  statusHistory: [{
    status: String,
    timestamp: { type: Date, default: Date.now },
    note: String,
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  }],
  
  // Payment Information
  payment: {
    method: {
      type: String,
      enum: ['cod', 'online', 'wallet', 'bank_transfer'],
      required: true
    },
    status: {
      type: String,
      enum: ['pending', 'paid', 'failed', 'refunded', 'partial'],
      default: 'pending'
    },
    transactionId: String,
    gatewayOrderId: String,
    gatewayPaymentId: String,
    paidAmount: { type: Number, default: 0 },
    paidAt: Date,
    refundAmount: { type: Number, default: 0 },
    refundedAt: Date
  },
  
  // Communication
  notes: [{
    message: String,
    timestamp: { type: Date, default: Date.now },
    author: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    isInternal: { type: Boolean, default: false }
  }],
  
  // Quality and Feedback
  feedback: {
    rating: {
      overall: { type: Number, min: 1, max: 5 },
      quality: { type: Number, min: 1, max: 5 },
      delivery: { type: Number, min: 1, max: 5 },
      service: { type: Number, min: 1, max: 5 }
    },
    review: String,
    images: [String],
    submittedAt: Date
  },
  
  // Cancellation Information
  cancellation: {
    reason: String,
    cancelledBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    cancelledAt: Date,
    refundAmount: Number,
    refundStatus: {
      type: String,
      enum: ['pending', 'processed', 'failed']
    }
  },
  
  // Return Information
  return: {
    reason: String,
    requestedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    requestedAt: Date,
    approvedAt: Date,
    items: [{
      materialId: mongoose.Schema.Types.ObjectId,
      quantity: Number,
      reason: String
    }],
    refundAmount: Number,
    status: {
      type: String,
      enum: ['requested', 'approved', 'rejected', 'completed']
    }
  },
  
  // Timestamps
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
orderSchema.index({ orderNumber: 1 });
orderSchema.index({ customerId: 1 });
orderSchema.index({ vendorId: 1 });
orderSchema.index({ status: 1 });
orderSchema.index({ 'payment.status': 1 });
orderSchema.index({ createdAt: -1 });
orderSchema.index({ 'delivery.timeSlot.date': 1 });

// Virtual for total items count
orderSchema.virtual('totalItems').get(function() {
  return this.items.reduce((total, item) => total + item.quantity, 0);
});

// Virtual for order age
orderSchema.virtual('orderAge').get(function() {
  return Date.now() - this.createdAt.getTime();
});

// Virtual for is cancellable
orderSchema.virtual('isCancellable').get(function() {
  const cancellableStatuses = ['pending', 'confirmed', 'preparing'];
  return cancellableStatuses.includes(this.status);
});

// Virtual for is returnable
orderSchema.virtual('isReturnable').get(function() {
  const returnableStatuses = ['delivered'];
  const maxReturnDays = 7 * 24 * 60 * 60 * 1000; // 7 days
  return returnableStatuses.includes(this.status) && 
         (Date.now() - this.createdAt.getTime()) <= maxReturnDays;
});

// Pre-save middleware to generate order number
orderSchema.pre('save', async function(next) {
  if (this.isNew && !this.orderNumber) {
    const count = await this.constructor.countDocuments();
    this.orderNumber = `ORD${Date.now()}${String(count + 1).padStart(4, '0')}`;
  }
  next();
});

// Pre-save middleware to update status history
orderSchema.pre('save', function(next) {
  if (this.isModified('status') && !this.isNew) {
    this.statusHistory.push({
      status: this.status,
      timestamp: new Date()
    });
  }
  next();
});

// Method to update status
orderSchema.methods.updateStatus = function(newStatus, note, updatedBy) {
  this.status = newStatus;
  this.statusHistory.push({
    status: newStatus,
    timestamp: new Date(),
    note,
    updatedBy
  });
  return this.save();
};

// Method to add note
orderSchema.methods.addNote = function(message, author, isInternal = false) {
  this.notes.push({
    message,
    author,
    isInternal,
    timestamp: new Date()
  });
  return this.save();
};

// Method to calculate delivery charges
orderSchema.methods.calculateDeliveryCharges = function(vendorDeliveryCharges, freeDeliveryThreshold) {
  if (freeDeliveryThreshold && this.pricing.subtotal >= freeDeliveryThreshold) {
    this.pricing.delivery.charges = 0;
    this.pricing.delivery.freeDelivery = true;
  } else {
    this.pricing.delivery.charges = vendorDeliveryCharges || 0;
    this.pricing.delivery.freeDelivery = false;
  }
  
  this.pricing.total = this.pricing.subtotal + 
                      this.pricing.tax.total + 
                      this.pricing.delivery.charges + 
                      this.pricing.packaging.charges - 
                      this.pricing.discount.amount;
  
  return this.save();
};

// Method to generate delivery OTP
orderSchema.methods.generateDeliveryOTP = function() {
  const otp = Math.floor(1000 + Math.random() * 9000).toString();
  this.delivery.otp.code = otp;
  this.delivery.otp.verified = false;
  return otp;
};

// Method to verify delivery OTP
orderSchema.methods.verifyDeliveryOTP = function(candidateOTP) {
  if (this.delivery.otp.code === candidateOTP) {
    this.delivery.otp.verified = true;
    this.delivery.actualDeliveryTime = new Date();
    this.status = 'delivered';
    return true;
  }
  return false;
};

// Static method to get orders by status
orderSchema.statics.getOrdersByStatus = function(status, options = {}) {
  return this.find({ status })
    .populate('customerId', 'name phone')
    .populate('vendorId', 'businessName')
    .populate('items.materialId', 'name images')
    .sort(options.sort || { createdAt: -1 })
    .limit(options.limit || 50);
};

// Static method to get vendor orders
orderSchema.statics.getVendorOrders = function(vendorId, options = {}) {
  return this.find({ vendorId })
    .populate('customerId', 'name phone location')
    .populate('items.materialId', 'name images')
    .sort(options.sort || { createdAt: -1 })
    .limit(options.limit || 50);
};

module.exports = mongoose.model('Order', orderSchema);
