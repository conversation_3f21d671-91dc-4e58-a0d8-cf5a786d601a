#!/bin/bash

# 🚀 BuildBid Production Deployment Script
# Deploys BuildBid to handle 10M users with enterprise-grade infrastructure

set -e

echo "🚀 Starting BuildBid Production Deployment..."

# Configuration
PROJECT_NAME="buildbid-production"
REGION="us-central1"
FIREBASE_PROJECT="buildbid-production"
DOCKER_IMAGE="buildbid/api"
VERSION=$(date +%Y%m%d-%H%M%S)

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check if required tools are installed
    command -v flutter >/dev/null 2>&1 || { print_error "Flutter is required but not installed."; exit 1; }
    command -v firebase >/dev/null 2>&1 || { print_error "Firebase CLI is required but not installed."; exit 1; }
    command -v docker >/dev/null 2>&1 || { print_error "Docker is required but not installed."; exit 1; }
    command -v gcloud >/dev/null 2>&1 || { print_error "Google Cloud CLI is required but not installed."; exit 1; }
    
    print_success "All prerequisites are installed"
}

# Setup Firebase project
setup_firebase() {
    print_status "Setting up Firebase project..."
    
    # Login to Firebase
    firebase login --ci
    
    # Set Firebase project
    firebase use $FIREBASE_PROJECT
    
    # Deploy Firestore rules and indexes
    firebase deploy --only firestore:rules,firestore:indexes
    
    # Deploy Firebase Functions
    cd firebase/functions
    npm install
    npm run build
    cd ../..
    firebase deploy --only functions
    
    # Deploy Firebase Hosting (for admin panel)
    firebase deploy --only hosting
    
    print_success "Firebase setup completed"
}

# Build Flutter apps
build_flutter_apps() {
    print_status "Building Flutter applications..."
    
    # Build main Flutter app
    print_status "Building main BuildBid app..."
    flutter clean
    flutter pub get
    flutter build apk --release --split-per-abi
    flutter build appbundle --release
    flutter build web --release
    
    # Build admin panel
    print_status "Building admin panel..."
    cd buildbid_admin
    flutter clean
    flutter pub get
    flutter build web --release
    cd ..
    
    print_success "Flutter apps built successfully"
}

# Deploy backend services
deploy_backend() {
    print_status "Deploying backend services..."
    
    # Build Docker image
    cd backend
    docker build -t $DOCKER_IMAGE:$VERSION .
    docker tag $DOCKER_IMAGE:$VERSION $DOCKER_IMAGE:latest
    
    # Push to container registry
    docker push $DOCKER_IMAGE:$VERSION
    docker push $DOCKER_IMAGE:latest
    
    # Deploy to Google Cloud Run
    gcloud run deploy buildbid-api \
        --image $DOCKER_IMAGE:latest \
        --platform managed \
        --region $REGION \
        --allow-unauthenticated \
        --memory 2Gi \
        --cpu 2 \
        --max-instances 100 \
        --set-env-vars NODE_ENV=production
    
    cd ..
    print_success "Backend deployed successfully"
}

# Setup monitoring and alerting
setup_monitoring() {
    print_status "Setting up monitoring and alerting..."
    
    # Deploy monitoring stack
    kubectl apply -f k8s/monitoring/
    
    # Setup Grafana dashboards
    kubectl apply -f k8s/grafana/
    
    # Configure alerts
    kubectl apply -f k8s/alerts/
    
    print_success "Monitoring setup completed"
}

# Setup database
setup_database() {
    print_status "Setting up production database..."
    
    # MongoDB Atlas setup (via API)
    curl -X POST \
        --digest -u "$MONGODB_USER:$MONGODB_PASSWORD" \
        --header "Content-Type: application/json" \
        "https://cloud.mongodb.com/api/atlas/v1.0/groups/$MONGODB_PROJECT_ID/clusters" \
        --data '{
            "name": "buildbid-production",
            "diskSizeGB": 100,
            "numShards": 3,
            "providerSettings": {
                "providerName": "GCP",
                "instanceSizeName": "M60",
                "regionName": "US_CENTRAL1"
            }
        }'
    
    # Redis setup
    gcloud redis instances create buildbid-cache \
        --size=5 \
        --region=$REGION \
        --redis-version=redis_6_x \
        --tier=standard
    
    print_success "Database setup completed"
}

# Setup CDN and load balancer
setup_cdn() {
    print_status "Setting up CDN and load balancer..."
    
    # Create load balancer
    gcloud compute url-maps create buildbid-lb \
        --default-service buildbid-backend-service
    
    # Setup SSL certificate
    gcloud compute ssl-certificates create buildbid-ssl \
        --domains api.buildbid.com,app.buildbid.com
    
    # Create HTTPS proxy
    gcloud compute target-https-proxies create buildbid-https-proxy \
        --url-map buildbid-lb \
        --ssl-certificates buildbid-ssl
    
    # Setup Cloudflare CDN
    curl -X POST "https://api.cloudflare.com/client/v4/zones" \
        -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN" \
        -H "Content-Type: application/json" \
        --data '{
            "name": "buildbid.com",
            "type": "full"
        }'
    
    print_success "CDN and load balancer setup completed"
}

# Run performance tests
run_performance_tests() {
    print_status "Running performance tests..."
    
    # Load testing with Artillery
    cd tests/performance
    npm install
    npm run test:load
    npm run test:stress
    cd ../..
    
    print_success "Performance tests completed"
}

# Setup security
setup_security() {
    print_status "Setting up security measures..."
    
    # Setup WAF rules
    gcloud compute security-policies create buildbid-security-policy \
        --description "Security policy for BuildBid"
    
    # Rate limiting rules
    gcloud compute security-policies rules create 1000 \
        --security-policy buildbid-security-policy \
        --expression "origin.region_code == 'US'" \
        --action "rate_based_ban" \
        --rate-limit-threshold-count 100 \
        --rate-limit-threshold-interval-sec 60
    
    # Setup SSL/TLS
    gcloud compute ssl-policies create buildbid-ssl-policy \
        --profile MODERN \
        --min-tls-version 1.2
    
    print_success "Security setup completed"
}

# Deploy mobile apps
deploy_mobile_apps() {
    print_status "Preparing mobile app deployment..."
    
    # Android - Upload to Play Console (manual step)
    print_warning "Android APK ready at: build/app/outputs/flutter-apk/"
    print_warning "Android Bundle ready at: build/app/outputs/bundle/release/"
    print_warning "Please upload to Google Play Console manually"
    
    # iOS - Upload to App Store (manual step)
    print_warning "iOS build ready for App Store submission"
    print_warning "Please use Xcode to upload to App Store Connect"
    
    print_success "Mobile apps prepared for deployment"
}

# Health checks
run_health_checks() {
    print_status "Running health checks..."
    
    # API health check
    API_URL="https://api.buildbid.com/health"
    if curl -f $API_URL; then
        print_success "API health check passed"
    else
        print_error "API health check failed"
        exit 1
    fi
    
    # Database connectivity
    if mongo --eval "db.adminCommand('ping')" $MONGODB_URI; then
        print_success "Database connectivity check passed"
    else
        print_error "Database connectivity check failed"
        exit 1
    fi
    
    # Redis connectivity
    if redis-cli -u $REDIS_URI ping; then
        print_success "Redis connectivity check passed"
    else
        print_error "Redis connectivity check failed"
        exit 1
    fi
    
    print_success "All health checks passed"
}

# Main deployment function
main() {
    print_status "🚀 BuildBid Production Deployment Started"
    print_status "Version: $VERSION"
    print_status "Target: Production (10M users scale)"
    
    check_prerequisites
    setup_firebase
    build_flutter_apps
    deploy_backend
    setup_database
    setup_cdn
    setup_monitoring
    setup_security
    run_performance_tests
    deploy_mobile_apps
    run_health_checks
    
    print_success "🎉 BuildBid Production Deployment Completed Successfully!"
    print_success "🌐 API: https://api.buildbid.com"
    print_success "📱 App: https://app.buildbid.com"
    print_success "👨‍💼 Admin: https://admin.buildbid.com"
    print_success "📊 Monitoring: https://monitoring.buildbid.com"
    
    print_status "📈 Your BuildBid app is now ready to handle 10M users!"
}

# Run main function
main "$@"
