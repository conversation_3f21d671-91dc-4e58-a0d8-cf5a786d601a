import 'package:flutter/material.dart';
import '../models/requirement_model.dart';
import '../models/bid_model.dart';
import '../services/sample_data_service.dart';

class RequirementProvider extends ChangeNotifier {
  List<RequirementModel> _requirements = [];
  List<RequirementModel> _activeRequirements = [];
  List<BidModel> _bids = [];
  List<BidModel> _requirementBids = [];
  RequirementModel? _selectedRequirement;
  bool _isLoading = false;
  String? _errorMessage;

  // Getters
  List<RequirementModel> get requirements => _requirements;
  List<RequirementModel> get activeRequirements => _activeRequirements;
  List<BidModel> get bids => _bids;
  List<BidModel> get requirementBids => _requirementBids;
  RequirementModel? get selectedRequirement => _selectedRequirement;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  // REQUIREMENT OPERATIONS

  // Create requirement
  Future<bool> createRequirement(RequirementModel requirement) async {
    _setLoading(true);
    _clearError();

    try {
      await Future.delayed(const Duration(milliseconds: 500)); // Simulate network delay
      
      String requirementId = 'req_${DateTime.now().millisecondsSinceEpoch}';
      RequirementModel updatedRequirement = requirement.copyWith(id: requirementId);
      _requirements.insert(0, updatedRequirement);

      _setLoading(false);
      return true;
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
      return false;
    }
  }

  // Post requirement (alias for createRequirement)
  Future<bool> postRequirement(RequirementModel requirement) async {
    return await createRequirement(requirement);
  }

  // Load requirements by builder
  Future<void> loadRequirementsByBuilder(String builderId) async {
    _setLoading(true);
    _clearError();

    try {
      await Future.delayed(const Duration(milliseconds: 500)); // Simulate network delay
      _requirements = SampleDataService.getSampleRequirements();
      _setLoading(false);
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
    }
  }

  // Load active requirements for vendors
  Future<void> loadActiveRequirements({String? city, String? materialType}) async {
    _setLoading(true);
    _clearError();

    try {
      await Future.delayed(const Duration(milliseconds: 500)); // Simulate network delay
      _activeRequirements = SampleDataService.getSampleRequirements()
          .where((req) => req.status == 'active')
          .toList();
      
      if (city != null) {
        _activeRequirements = _activeRequirements
            .where((req) => req.city.toLowerCase() == city.toLowerCase())
            .toList();
      }
      
      if (materialType != null) {
        _activeRequirements = _activeRequirements
            .where((req) => req.materialType.toLowerCase() == materialType.toLowerCase())
            .toList();
      }
      
      _setLoading(false);
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
    }
  }

  // Update requirement
  Future<bool> updateRequirement(RequirementModel requirement) async {
    _setLoading(true);
    _clearError();

    try {
      await Future.delayed(const Duration(milliseconds: 300)); // Simulate network delay

      // Update local list
      int index = _requirements.indexWhere((r) => r.id == requirement.id);
      if (index != -1) {
        _requirements[index] = requirement;
      }

      // Update selected requirement if it's the same
      if (_selectedRequirement?.id == requirement.id) {
        _selectedRequirement = requirement;
      }

      _setLoading(false);
      return true;
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
      return false;
    }
  }

  // Delete requirement
  Future<bool> deleteRequirement(String requirementId) async {
    _setLoading(true);
    _clearError();

    try {
      await Future.delayed(const Duration(milliseconds: 300)); // Simulate network delay

      // Remove from local list
      _requirements.removeWhere((r) => r.id == requirementId);
      _activeRequirements.removeWhere((r) => r.id == requirementId);

      // Clear selected requirement if it's the deleted one
      if (_selectedRequirement?.id == requirementId) {
        _selectedRequirement = null;
      }

      _setLoading(false);
      return true;
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
      return false;
    }
  }

  // BID OPERATIONS

  // Create bid
  Future<bool> createBid(BidModel bid) async {
    _setLoading(true);
    _clearError();

    try {
      await Future.delayed(const Duration(milliseconds: 500)); // Simulate network delay
      
      String bidId = 'bid_${DateTime.now().millisecondsSinceEpoch}';
      BidModel updatedBid = bid.copyWith(id: bidId);
      _bids.insert(0, updatedBid);

      _setLoading(false);
      return true;
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
      return false;
    }
  }

  // Load bids for requirement
  Future<void> loadBidsForRequirement(String requirementId) async {
    _setLoading(true);
    _clearError();

    try {
      await Future.delayed(const Duration(milliseconds: 300)); // Simulate network delay
      _requirementBids = SampleDataService.getSampleBids()
          .where((bid) => bid.requirementId == requirementId)
          .toList();
      _setLoading(false);
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
    }
  }

  // Load bids by vendor
  Future<void> loadBidsByVendor(String vendorId) async {
    _setLoading(true);
    _clearError();

    try {
      await Future.delayed(const Duration(milliseconds: 300)); // Simulate network delay
      _bids = SampleDataService.getSampleBids()
          .where((bid) => bid.vendorId == vendorId)
          .toList();
      _setLoading(false);
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
    }
  }

  // Update bid
  Future<bool> updateBid(BidModel bid) async {
    _setLoading(true);
    _clearError();

    try {
      await Future.delayed(const Duration(milliseconds: 300)); // Simulate network delay

      // Update local lists
      int index = _bids.indexWhere((b) => b.id == bid.id);
      if (index != -1) {
        _bids[index] = bid;
      }

      int reqBidIndex = _requirementBids.indexWhere((b) => b.id == bid.id);
      if (reqBidIndex != -1) {
        _requirementBids[reqBidIndex] = bid;
      }

      _setLoading(false);
      return true;
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
      return false;
    }
  }

  // Accept bid
  Future<bool> acceptBid(String bidId, String requirementId) async {
    _setLoading(true);
    _clearError();

    try {
      await Future.delayed(const Duration(milliseconds: 300)); // Simulate network delay

      // Update bid status to accepted
      int bidIndex = _bids.indexWhere((b) => b.id == bidId);
      if (bidIndex != -1) {
        _bids[bidIndex] = _bids[bidIndex].copyWith(status: 'accepted');
      }

      int reqBidIndex = _requirementBids.indexWhere((b) => b.id == bidId);
      if (reqBidIndex != -1) {
        _requirementBids[reqBidIndex] = _requirementBids[reqBidIndex].copyWith(status: 'accepted');
      }

      _setLoading(false);
      return true;
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
      return false;
    }
  }

  // UTILITY METHODS

  // Set selected requirement
  void setSelectedRequirement(RequirementModel? requirement) {
    _selectedRequirement = requirement;
    notifyListeners();
  }

  // Get requirement by ID
  RequirementModel? getRequirementById(String id) {
    try {
      return _requirements.firstWhere((r) => r.id == id);
    } catch (e) {
      try {
        return _activeRequirements.firstWhere((r) => r.id == id);
      } catch (e) {
        return null;
      }
    }
  }

  // Get bid by ID
  BidModel? getBidById(String id) {
    try {
      return _bids.firstWhere((b) => b.id == id);
    } catch (e) {
      try {
        return _requirementBids.firstWhere((b) => b.id == id);
      } catch (e) {
        return null;
      }
    }
  }

  // Get unread bid count for builder
  int getUnreadBidCount() {
    return _requirements
        .where((req) => req.status == 'active' && req.bidCount > 0)
        .length;
  }

  // Get total bids count
  int get totalBids {
    return _requirements.fold(0, (sum, req) => sum + req.bidCount);
  }

  // Get active bids for vendor
  List<BidModel> getActiveBids() {
    return _bids.where((b) => b.status == 'active').toList();
  }

  // Get winning bids for vendor
  List<BidModel> getWinningBids() {
    return _bids.where((b) => b.status == 'accepted' || b.status == 'won').toList();
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }
}
