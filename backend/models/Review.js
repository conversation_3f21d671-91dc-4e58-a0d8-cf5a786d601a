const mongoose = require('mongoose');

const reviewSchema = new mongoose.Schema({
  // Review Type
  reviewType: {
    type: String,
    enum: ['vendor', 'service', 'order'],
    required: true
  },
  
  // Reviewer Information
  reviewerId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  
  // Target Information (what is being reviewed)
  targetId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    refPath: 'targetModel'
  },
  targetModel: {
    type: String,
    required: true,
    enum: ['Vendor', 'Service', 'Order']
  },
  
  // Order Reference (for order-based reviews)
  orderId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Order'
  },
  
  // Service Booking Reference (for service reviews)
  serviceBookingId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'ServiceBooking'
  },
  
  // Rating Details
  rating: {
    overall: {
      type: Number,
      required: [true, 'Overall rating is required'],
      min: [1, 'Rating must be at least 1'],
      max: [5, 'Rating cannot exceed 5']
    },
    // For vendor/material reviews
    quality: {
      type: Number,
      min: [1, 'Quality rating must be at least 1'],
      max: [5, 'Quality rating cannot exceed 5']
    },
    delivery: {
      type: Number,
      min: [1, 'Delivery rating must be at least 1'],
      max: [5, 'Delivery rating cannot exceed 5']
    },
    service: {
      type: Number,
      min: [1, 'Service rating must be at least 1'],
      max: [5, 'Service rating cannot exceed 5']
    },
    // For service provider reviews
    punctuality: {
      type: Number,
      min: [1, 'Punctuality rating must be at least 1'],
      max: [5, 'Punctuality rating cannot exceed 5']
    },
    behavior: {
      type: Number,
      min: [1, 'Behavior rating must be at least 1'],
      max: [5, 'Behavior rating cannot exceed 5']
    },
    workQuality: {
      type: Number,
      min: [1, 'Work quality rating must be at least 1'],
      max: [5, 'Work quality rating cannot exceed 5']
    }
  },
  
  // Review Content
  title: {
    type: String,
    trim: true,
    maxlength: [200, 'Review title cannot exceed 200 characters']
  },
  comment: {
    type: String,
    required: [true, 'Review comment is required'],
    trim: true,
    maxlength: [1000, 'Review comment cannot exceed 1000 characters']
  },
  
  // Review Images
  images: [{
    url: String,
    publicId: String,
    caption: String
  }],
  
  // Review Tags
  tags: [{
    type: String,
    enum: [
      'excellent_quality', 'good_value', 'fast_delivery', 'professional',
      'punctual', 'skilled', 'polite', 'clean_work', 'reliable',
      'poor_quality', 'delayed', 'unprofessional', 'rude', 'overpriced'
    ]
  }],
  
  // Verification
  isVerified: {
    type: Boolean,
    default: false
  },
  verifiedPurchase: {
    type: Boolean,
    default: false
  },
  
  // Moderation
  isApproved: {
    type: Boolean,
    default: true
  },
  isFlagged: {
    type: Boolean,
    default: false
  },
  flagReason: {
    type: String,
    enum: ['spam', 'inappropriate', 'fake', 'offensive', 'other']
  },
  moderatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  moderatedAt: Date,
  
  // Helpfulness
  helpfulVotes: {
    type: Number,
    default: 0
  },
  unhelpfulVotes: {
    type: Number,
    default: 0
  },
  votedBy: [{
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    vote: {
      type: String,
      enum: ['helpful', 'unhelpful']
    },
    votedAt: {
      type: Date,
      default: Date.now
    }
  }],
  
  // Response from Business
  response: {
    message: String,
    respondedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    respondedAt: Date
  },
  
  // Timestamps
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
reviewSchema.index({ reviewerId: 1 });
reviewSchema.index({ targetId: 1, targetModel: 1 });
reviewSchema.index({ orderId: 1 });
reviewSchema.index({ serviceBookingId: 1 });
reviewSchema.index({ 'rating.overall': -1 });
reviewSchema.index({ isApproved: 1, isFlagged: 1 });
reviewSchema.index({ createdAt: -1 });

// Compound indexes
reviewSchema.index({ targetId: 1, targetModel: 1, isApproved: 1 });
reviewSchema.index({ reviewerId: 1, targetId: 1, targetModel: 1 }, { unique: true });

// Virtual for helpfulness ratio
reviewSchema.virtual('helpfulnessRatio').get(function() {
  const total = this.helpfulVotes + this.unhelpfulVotes;
  if (total === 0) return 0;
  return Math.round((this.helpfulVotes / total) * 100);
});

// Virtual for review age
reviewSchema.virtual('reviewAge').get(function() {
  const now = new Date();
  const diffTime = Math.abs(now - this.createdAt);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  if (diffDays === 1) return '1 day ago';
  if (diffDays < 30) return `${diffDays} days ago`;
  if (diffDays < 365) return `${Math.floor(diffDays / 30)} months ago`;
  return `${Math.floor(diffDays / 365)} years ago`;
});

// Virtual for average rating (for multi-aspect reviews)
reviewSchema.virtual('averageRating').get(function() {
  const ratings = [];
  
  if (this.rating.quality) ratings.push(this.rating.quality);
  if (this.rating.delivery) ratings.push(this.rating.delivery);
  if (this.rating.service) ratings.push(this.rating.service);
  if (this.rating.punctuality) ratings.push(this.rating.punctuality);
  if (this.rating.behavior) ratings.push(this.rating.behavior);
  if (this.rating.workQuality) ratings.push(this.rating.workQuality);
  
  if (ratings.length === 0) return this.rating.overall;
  
  const sum = ratings.reduce((acc, rating) => acc + rating, 0);
  return Math.round((sum / ratings.length) * 10) / 10;
});

// Method to vote on review helpfulness
reviewSchema.methods.voteHelpfulness = function(userId, vote) {
  // Remove existing vote by this user
  this.votedBy = this.votedBy.filter(v => !v.userId.equals(userId));
  
  // Add new vote
  this.votedBy.push({ userId, vote });
  
  // Update vote counts
  this.helpfulVotes = this.votedBy.filter(v => v.vote === 'helpful').length;
  this.unhelpfulVotes = this.votedBy.filter(v => v.vote === 'unhelpful').length;
  
  return this.save();
};

// Method to add business response
reviewSchema.methods.addResponse = function(message, respondedBy) {
  this.response = {
    message,
    respondedBy,
    respondedAt: new Date()
  };
  return this.save();
};

// Method to flag review
reviewSchema.methods.flagReview = function(reason, moderatedBy) {
  this.isFlagged = true;
  this.flagReason = reason;
  this.moderatedBy = moderatedBy;
  this.moderatedAt = new Date();
  return this.save();
};

// Static method to get reviews for target
reviewSchema.statics.getReviewsForTarget = function(targetId, targetModel, options = {}) {
  const query = {
    targetId,
    targetModel,
    isApproved: true,
    isFlagged: false
  };
  
  return this.find(query)
    .populate('reviewerId', 'name profileImage')
    .populate('response.respondedBy', 'name')
    .sort(options.sort || { createdAt: -1 })
    .limit(options.limit || 20);
};

// Static method to get review statistics
reviewSchema.statics.getReviewStats = function(targetId, targetModel) {
  return this.aggregate([
    {
      $match: {
        targetId: new mongoose.Types.ObjectId(targetId),
        targetModel,
        isApproved: true,
        isFlagged: false
      }
    },
    {
      $group: {
        _id: null,
        totalReviews: { $sum: 1 },
        averageRating: { $avg: '$rating.overall' },
        ratingDistribution: {
          $push: '$rating.overall'
        },
        averageQuality: { $avg: '$rating.quality' },
        averageDelivery: { $avg: '$rating.delivery' },
        averageService: { $avg: '$rating.service' },
        averagePunctuality: { $avg: '$rating.punctuality' },
        averageBehavior: { $avg: '$rating.behavior' },
        averageWorkQuality: { $avg: '$rating.workQuality' }
      }
    },
    {
      $project: {
        _id: 0,
        totalReviews: 1,
        averageRating: { $round: ['$averageRating', 1] },
        ratingBreakdown: {
          5: {
            $size: {
              $filter: {
                input: '$ratingDistribution',
                cond: { $eq: ['$$this', 5] }
              }
            }
          },
          4: {
            $size: {
              $filter: {
                input: '$ratingDistribution',
                cond: { $eq: ['$$this', 4] }
              }
            }
          },
          3: {
            $size: {
              $filter: {
                input: '$ratingDistribution',
                cond: { $eq: ['$$this', 3] }
              }
            }
          },
          2: {
            $size: {
              $filter: {
                input: '$ratingDistribution',
                cond: { $eq: ['$$this', 2] }
              }
            }
          },
          1: {
            $size: {
              $filter: {
                input: '$ratingDistribution',
                cond: { $eq: ['$$this', 1] }
              }
            }
          }
        },
        aspectRatings: {
          quality: { $round: ['$averageQuality', 1] },
          delivery: { $round: ['$averageDelivery', 1] },
          service: { $round: ['$averageService', 1] },
          punctuality: { $round: ['$averagePunctuality', 1] },
          behavior: { $round: ['$averageBehavior', 1] },
          workQuality: { $round: ['$averageWorkQuality', 1] }
        }
      }
    }
  ]);
};

module.exports = mongoose.model('Review', reviewSchema);
