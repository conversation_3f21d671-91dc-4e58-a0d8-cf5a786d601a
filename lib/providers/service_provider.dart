import 'package:flutter/material.dart';
import '../models/service_model.dart';
import '../services/sample_data_service.dart';

class ServiceProvider extends ChangeNotifier {
  List<ServiceModel> _services = [];
  List<ServiceModel> _nearbyServices = [];
  List<ServiceModel> _filteredServices = [];
  List<ServiceBookingModel> _bookings = [];
  bool _isLoading = false;
  String? _errorMessage;
  String _selectedCategory = '';
  String _searchQuery = '';

  // Filter options
  double _minRating = 0.0;
  double _minPrice = 0.0;
  double _maxPrice = 1000.0;
  int _minExperience = 0;
  bool _availableToday = false;

  // Getters
  List<ServiceModel> get services => _services;
  List<ServiceModel> get nearbyServices => _nearbyServices;
  List<ServiceModel> get filteredServices => _filteredServices;
  List<ServiceBookingModel> get bookings => _bookings;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  String get selectedCategory => _selectedCategory;
  String get searchQuery => _searchQuery;
  double get minRating => _minRating;
  double get minPrice => _minPrice;
  double get maxPrice => _maxPrice;
  int get minExperience => _minExperience;
  bool get availableToday => _availableToday;

  // Load services
  Future<void> loadServices({String? category}) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      await Future.delayed(const Duration(milliseconds: 500)); // Simulate network delay
      
      _services = SampleDataService.getSampleServices();
      
      if (category != null && category.isNotEmpty) {
        _services = _services.where((s) => s.category.toLowerCase() == category.toLowerCase()).toList();
      }

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _errorMessage = e.toString();
      _isLoading = false;
      notifyListeners();
    }
  }

  // Filter by category
  void filterByCategory(String category) {
    _selectedCategory = category;
    loadServices(category: category);
  }

  // Book service
  Future<bool> bookService(ServiceBookingModel booking) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      await Future.delayed(const Duration(milliseconds: 800)); // Simulate network delay

      // Add booking with generated ID
      final bookingWithId = ServiceBookingModel(
        id: 'booking_${DateTime.now().millisecondsSinceEpoch}',
        serviceId: booking.serviceId,
        serviceName: booking.serviceName,
        providerId: booking.providerId,
        providerName: booking.providerName,
        providerPhone: booking.providerPhone,
        customerId: booking.customerId,
        customerName: booking.customerName,
        customerPhone: booking.customerPhone,
        bookingDate: booking.bookingDate,
        timeSlot: booking.timeSlot,
        numberOfDays: booking.numberOfDays,
        totalAmount: booking.totalAmount,
        workAddress: booking.workAddress,
        city: booking.city,
        state: booking.state,
        pincode: booking.pincode,
        status: 'pending',
        paymentStatus: 'pending',
        notes: booking.notes,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      _bookings.insert(0, bookingWithId);
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _errorMessage = e.toString();
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  // Load user bookings
  Future<void> loadUserBookings(String userId) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      await Future.delayed(const Duration(milliseconds: 300)); // Simulate network delay
      
      // Create sample bookings if none exist
      if (_bookings.isEmpty) {
        _bookings = SampleDataService.getSampleServiceBookings(userId);
      }

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _errorMessage = e.toString();
      _isLoading = false;
      notifyListeners();
    }
  }

  // Update booking status
  Future<bool> updateBookingStatus(String bookingId, String status) async {
    try {
      await Future.delayed(const Duration(milliseconds: 300)); // Simulate network delay

      final index = _bookings.indexWhere((booking) => booking.id == bookingId);
      if (index >= 0) {
        _bookings[index] = ServiceBookingModel(
          id: _bookings[index].id,
          serviceId: _bookings[index].serviceId,
          serviceName: _bookings[index].serviceName,
          providerId: _bookings[index].providerId,
          providerName: _bookings[index].providerName,
          providerPhone: _bookings[index].providerPhone,
          customerId: _bookings[index].customerId,
          customerName: _bookings[index].customerName,
          customerPhone: _bookings[index].customerPhone,
          bookingDate: _bookings[index].bookingDate,
          timeSlot: _bookings[index].timeSlot,
          numberOfDays: _bookings[index].numberOfDays,
          totalAmount: _bookings[index].totalAmount,
          workAddress: _bookings[index].workAddress,
          city: _bookings[index].city,
          state: _bookings[index].state,
          pincode: _bookings[index].pincode,
          status: status,
          paymentStatus: _bookings[index].paymentStatus,
          notes: _bookings[index].notes,
          createdAt: _bookings[index].createdAt,
          updatedAt: DateTime.now(),
          cancellationReason: _bookings[index].cancellationReason,
          rating: _bookings[index].rating,
          review: _bookings[index].review,
        );
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Cancel booking
  Future<bool> cancelBooking(String bookingId, String reason) async {
    try {
      await Future.delayed(const Duration(milliseconds: 300)); // Simulate network delay

      final index = _bookings.indexWhere((booking) => booking.id == bookingId);
      if (index >= 0) {
        _bookings[index] = ServiceBookingModel(
          id: _bookings[index].id,
          serviceId: _bookings[index].serviceId,
          serviceName: _bookings[index].serviceName,
          providerId: _bookings[index].providerId,
          providerName: _bookings[index].providerName,
          providerPhone: _bookings[index].providerPhone,
          customerId: _bookings[index].customerId,
          customerName: _bookings[index].customerName,
          customerPhone: _bookings[index].customerPhone,
          bookingDate: _bookings[index].bookingDate,
          timeSlot: _bookings[index].timeSlot,
          numberOfDays: _bookings[index].numberOfDays,
          totalAmount: _bookings[index].totalAmount,
          workAddress: _bookings[index].workAddress,
          city: _bookings[index].city,
          state: _bookings[index].state,
          pincode: _bookings[index].pincode,
          status: 'cancelled',
          paymentStatus: _bookings[index].paymentStatus,
          notes: _bookings[index].notes,
          createdAt: _bookings[index].createdAt,
          updatedAt: DateTime.now(),
          cancellationReason: reason,
          rating: _bookings[index].rating,
          review: _bookings[index].review,
        );
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Get service by ID
  ServiceModel? getServiceById(String serviceId) {
    try {
      return _services.firstWhere((service) => service.id == serviceId);
    } catch (e) {
      return null;
    }
  }

  // Get booking by ID
  ServiceBookingModel? getBookingById(String bookingId) {
    try {
      return _bookings.firstWhere((booking) => booking.id == bookingId);
    } catch (e) {
      return null;
    }
  }

  // Get services by category
  List<ServiceModel> getServicesByCategory(String category) {
    return _services.where((service) => 
        service.category.toLowerCase() == category.toLowerCase()).toList();
  }

  // Get top rated services
  List<ServiceModel> getTopRatedServices() {
    return _services.where((service) => service.providerRating >= 4.0).take(10).toList();
  }

  // Get available services in location
  List<ServiceModel> getServicesInLocation(String city) {
    return _services.where((service) => 
        service.city.toLowerCase() == city.toLowerCase() && service.isAvailable).toList();
  }

  // Calculate service cost
  double calculateServiceCost(ServiceModel service, int numberOfDays, int numberOfHours) {
    if (service.unit == 'day') {
      return service.pricePerDay * numberOfDays;
    } else {
      return service.pricePerHour * numberOfHours;
    }
  }

  // Get active bookings
  List<ServiceBookingModel> getActiveBookings() {
    return _bookings.where((booking) => 
        ['pending', 'confirmed', 'in_progress'].contains(booking.status)).toList();
  }

  // Get completed bookings
  List<ServiceBookingModel> getCompletedBookings() {
    return _bookings.where((booking) => booking.status == 'completed').toList();
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
  }

  // Load nearby services based on location
  Future<void> loadNearbyServices(dynamic userLocation, {double radiusKm = 10.0}) async {
    _setLoading(true);
    _clearError();

    try {
      await Future.delayed(const Duration(milliseconds: 500));

      // Get all services and filter by location (mock implementation)
      List<ServiceModel> allServices = SampleDataService.getSampleServices();
      _nearbyServices = allServices.where((service) => service.isAvailable).toList();
      _filteredServices = List.from(_nearbyServices);

      _setLoading(false);
    } catch (e) {
      _setError(e.toString());
      _setLoading(false);
    }
  }

  // Search services
  void searchServices(String query) {
    _searchQuery = query;
    _applyFilters();
  }

  // Set category filter
  void setCategoryFilter(String category) {
    _selectedCategory = category;
    _applyFilters();
  }

  // Set rating filter
  void setRatingFilter(double minRating) {
    _minRating = minRating;
    _applyFilters();
  }

  // Set price range filter
  void setPriceRangeFilter(double minPrice, double maxPrice) {
    _minPrice = minPrice;
    _maxPrice = maxPrice;
    _applyFilters();
  }

  // Set experience filter
  void setExperienceFilter(int minExperience) {
    _minExperience = minExperience;
    _applyFilters();
  }

  // Set available today filter
  void setAvailableTodayFilter(bool availableToday) {
    _availableToday = availableToday;
    _applyFilters();
  }

  // Apply all filters
  void _applyFilters() {
    List<ServiceModel> filtered = List.from(_services);

    // Search filter
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((service) =>
          service.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          service.description.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          service.category.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          service.skills.any((skill) =>
              skill.toLowerCase().contains(_searchQuery.toLowerCase()))
      ).toList();
    }

    // Category filter
    if (_selectedCategory.isNotEmpty) {
      filtered = filtered.where((service) =>
          service.category.toLowerCase() == _selectedCategory.toLowerCase()
      ).toList();
    }

    // Rating filter
    if (_minRating > 0) {
      filtered = filtered.where((service) => service.rating >= _minRating).toList();
    }

    // Price filter
    filtered = filtered.where((service) =>
        service.pricePerHour >= _minPrice && service.pricePerHour <= _maxPrice).toList();

    // Experience filter
    if (_minExperience > 0) {
      filtered = filtered.where((service) => service.yearsExperience >= _minExperience).toList();
    }

    // Available today filter
    if (_availableToday) {
      filtered = filtered.where((service) => service.isAvailable).toList();
    }

    _filteredServices = filtered;
    notifyListeners();
  }

  // Clear all filters
  void clearFilters() {
    _selectedCategory = '';
    _searchQuery = '';
    _minRating = 0.0;
    _minPrice = 0.0;
    _maxPrice = 1000.0;
    _minExperience = 0;
    _availableToday = false;
    _filteredServices = List.from(_services);
    notifyListeners();
  }
}
