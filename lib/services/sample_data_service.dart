import '../models/inventory_model.dart';
import '../models/user_model.dart';
import '../models/requirement_model.dart';
import '../models/bid_model.dart';
import '../models/vendor_model.dart';
import '../models/location_model.dart';
import '../models/order_model.dart';
import '../models/cart_model.dart';
import '../models/notification_model.dart';
import '../models/product_model.dart';
import '../models/service_model.dart';

class SampleDataService {
  // Sample users (vendors)
  static List<UserModel> getSampleVendorUsers() {
    return [
      UserModel(
        id: 'vendor_1',
        uid: 'vendor_1',
        name: '<PERSON>esh Building Materials',
        email: '<EMAIL>',
        phone: '+91 **********',
        role: 'vendor',
        status: 'active',
        location: {
          'city': 'Mumbai',
          'state': 'Maharashtra',
          'pincode': '400001',
          'address': 'Shop 15, Building Materials Market, Dadar'
        },
        rating: 4.5,
        totalOrders: 150,
        completedOrders: 142,
        createdAt: DateTime.now().subtract(const Duration(days: 365)),
        businessName: 'Rajesh Building Materials',
        isVerified: true,
      ),
      UserModel(
        id: 'vendor_2',
        uid: 'vendor_2',
        name: 'Sharma Construction Supplies',
        email: '<EMAIL>',
        phone: '+91 **********',
        role: 'vendor',
        status: 'active',
        location: {
          'city': 'Delhi',
          'state': 'Delhi',
          'pincode': '110001',
          'address': 'Block A, Construction Market, Karol Bagh'
        },
        rating: 4.2,
        totalOrders: 89,
        completedOrders: 85,
        createdAt: DateTime.now().subtract(const Duration(days: 200)),
        businessName: 'Sharma Construction Supplies',
        isVerified: true,
      ),
      UserModel(
        id: 'vendor_3',
        uid: 'vendor_3',
        name: 'Gupta Steel & Cement',
        email: '<EMAIL>',
        phone: '+91 9876543212',
        role: 'vendor',
        status: 'active',
        location: {
          'city': 'Pune',
          'state': 'Maharashtra',
          'pincode': '411001',
          'address': 'Warehouse 7, Industrial Area, Pimpri'
        },
        rating: 4.7,
        totalOrders: 203,
        completedOrders: 198,
        createdAt: DateTime.now().subtract(const Duration(days: 500)),
        businessName: 'Gupta Steel & Cement',
        isVerified: true,
      ),
    ];
  }

  // Sample inventory items
  static List<InventoryModel> getInventoryItems() {
    final vendors = getSampleVendorUsers();
    return [
      // Cement items
      InventoryModel(
        id: 'inv_1',
        vendorId: vendors[0].id,
        vendorName: vendors[0].businessName!,
        materialType: 'cement',
        description: 'ACC Cement - Grade 53',
        pricePerUnit: 350.0,
        quantity: 500,
        unit: 'bags',
        inStock: true,
        photoUrls: [],
        status: 'active',
        vendorRating: vendors[0].rating,
        minOrderQuantity: 10,
        specifications: {
          'brand': 'ACC',
          'grade': '53',
          'weight': '50kg per bag'
        },
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      ),
      InventoryModel(
        id: 'inv_2',
        vendorId: vendors[1].id,
        vendorName: vendors[1].businessName!,
        materialType: 'cement',
        description: 'UltraTech Cement - Grade 43',
        pricePerUnit: 320.0,
        quantity: 300,
        unit: 'bags',
        inStock: true,
        photoUrls: [],
        status: 'active',
        vendorRating: vendors[1].rating,
        minOrderQuantity: 5,
        specifications: {
          'brand': 'UltraTech',
          'grade': '43',
          'weight': '50kg per bag'
        },
        createdAt: DateTime.now().subtract(const Duration(days: 25)),
        updatedAt: DateTime.now().subtract(const Duration(days: 2)),
      ),
      
      // Steel items
      InventoryModel(
        id: 'inv_3',
        vendorId: vendors[2].id,
        vendorName: vendors[2].businessName!,
        materialType: 'steel',
        description: 'TMT Steel Bars - 12mm',
        pricePerUnit: 55.0,
        quantity: 1000,
        unit: 'kg',
        inStock: true,
        photoUrls: [],
        status: 'active',
        vendorRating: vendors[2].rating,
        minOrderQuantity: 100,
        specifications: {
          'diameter': '12mm',
          'grade': 'Fe500',
          'length': '12 meters'
        },
        createdAt: DateTime.now().subtract(const Duration(days: 20)),
        updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      ),
      InventoryModel(
        id: 'inv_4',
        vendorId: vendors[0].id,
        vendorName: vendors[0].businessName!,
        materialType: 'steel',
        description: 'TMT Steel Bars - 16mm',
        pricePerUnit: 58.0,
        quantity: 800,
        unit: 'kg',
        inStock: true,
        photoUrls: [],
        status: 'active',
        vendorRating: vendors[0].rating,
        minOrderQuantity: 100,
        specifications: {
          'diameter': '16mm',
          'grade': 'Fe500',
          'length': '12 meters'
        },
        createdAt: DateTime.now().subtract(const Duration(days: 18)),
        updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      ),

      // Bricks
      InventoryModel(
        id: 'inv_5',
        vendorId: vendors[1].id,
        vendorName: vendors[1].businessName!,
        materialType: 'bricks',
        description: 'Red Clay Bricks - Standard',
        pricePerUnit: 8.0,
        quantity: 5000,
        unit: 'pieces',
        inStock: true,
        photoUrls: [],
        status: 'active',
        vendorRating: vendors[1].rating,
        minOrderQuantity: 100,
        specifications: {
          'type': 'Red Clay',
          'size': '230x110x70mm',
          'strength': 'Class A'
        },
        createdAt: DateTime.now().subtract(const Duration(days: 15)),
        updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      ),

      // Sand
      InventoryModel(
        id: 'inv_6',
        vendorId: vendors[2].id,
        vendorName: vendors[2].businessName!,
        materialType: 'sand',
        description: 'River Sand - Fine Grade',
        pricePerUnit: 1200.0,
        quantity: 50,
        unit: 'cubic meters',
        inStock: true,
        photoUrls: [],
        status: 'active',
        vendorRating: vendors[2].rating,
        minOrderQuantity: 5,
        specifications: {
          'type': 'River Sand',
          'grade': 'Fine',
          'moisture': 'Less than 5%'
        },
        createdAt: DateTime.now().subtract(const Duration(days: 10)),
        updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      ),

      // Aggregates
      InventoryModel(
        id: 'inv_7',
        vendorId: vendors[0].id,
        vendorName: vendors[0].businessName!,
        materialType: 'aggregates',
        description: '20mm Crushed Stone',
        pricePerUnit: 800.0,
        quantity: 100,
        unit: 'cubic meters',
        inStock: true,
        photoUrls: [],
        status: 'active',
        vendorRating: vendors[0].rating,
        minOrderQuantity: 10,
        specifications: {
          'size': '20mm',
          'type': 'Crushed Stone',
          'grade': 'Premium'
        },
        createdAt: DateTime.now().subtract(const Duration(days: 8)),
        updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      ),

      // Low stock item
      InventoryModel(
        id: 'inv_8',
        vendorId: vendors[1].id,
        vendorName: vendors[1].businessName!,
        materialType: 'cement',
        description: 'Ambuja Cement - Grade 53',
        pricePerUnit: 340.0,
        quantity: 5, // Low stock
        unit: 'bags',
        inStock: true,
        photoUrls: [],
        status: 'active',
        vendorRating: vendors[1].rating,
        minOrderQuantity: 5,
        specifications: {
          'brand': 'Ambuja',
          'grade': '53',
          'weight': '50kg per bag'
        },
        createdAt: DateTime.now().subtract(const Duration(days: 5)),
        updatedAt: DateTime.now().subtract(const Duration(hours: 2)),
      ),

      // Out of stock item
      InventoryModel(
        id: 'inv_9',
        vendorId: vendors[2].id,
        vendorName: vendors[2].businessName!,
        materialType: 'steel',
        description: 'TMT Steel Bars - 8mm',
        pricePerUnit: 52.0,
        quantity: 0, // Out of stock
        unit: 'kg',
        inStock: false,
        photoUrls: [],
        status: 'active',
        vendorRating: vendors[2].rating,
        minOrderQuantity: 100,
        specifications: {
          'diameter': '8mm',
          'grade': 'Fe500',
          'length': '12 meters'
        },
        createdAt: DateTime.now().subtract(const Duration(days: 3)),
        updatedAt: DateTime.now().subtract(const Duration(hours: 1)),
      ),
    ];
  }

  // Sample requirements
  static List<RequirementModel> getSampleRequirements() {
    return [
      RequirementModel(
        id: 'req_1',
        builderId: 'builder_1',
        builderName: 'Amit Construction',
        title: 'High Quality Cement for Foundation',
        description: 'Need ACC or UltraTech cement for residential building foundation. Grade 53 preferred.',
        materialType: 'cement',
        materialSubType: 'Grade 53',
        quantity: 100,
        unit: 'bags',
        deliveryLocation: 'Plot 123, Sector 15, Gurgaon, Haryana - 122001',
        city: 'Gurgaon',
        state: 'Haryana',
        pincode: '122001',
        requiredBy: DateTime.now().add(const Duration(days: 7)),
        budgetMin: 32000,
        budgetMax: 38000,
        status: 'active',
        bidCount: 5,
        createdAt: DateTime.now().subtract(const Duration(days: 2)),
        updatedAt: DateTime.now().subtract(const Duration(days: 2)),
      ),
      RequirementModel(
        id: 'req_2',
        builderId: 'builder_2',
        builderName: 'Sharma Builders',
        title: 'TMT Steel Bars for Commercial Project',
        description: 'Required Fe500 grade TMT bars, 12mm and 16mm diameter. Total 5 tons needed.',
        materialType: 'steel',
        materialSubType: 'TMT Fe500',
        quantity: 5000,
        unit: 'kg',
        deliveryLocation: 'Commercial Complex Site, Noida, UP - 201301',
        city: 'Noida',
        state: 'Uttar Pradesh',
        pincode: '201301',
        requiredBy: DateTime.now().add(const Duration(days: 10)),
        budgetMin: 250000,
        budgetMax: 300000,
        status: 'active',
        bidCount: 8,
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      ),
      RequirementModel(
        id: 'req_3',
        builderId: 'builder_1',
        builderName: 'Amit Construction',
        title: 'Red Clay Bricks for Boundary Wall',
        description: 'Standard size red clay bricks for boundary wall construction. Class A quality required.',
        materialType: 'bricks',
        materialSubType: 'Red Clay',
        quantity: 10000,
        unit: 'pieces',
        deliveryLocation: 'Residential Plot, Faridabad, Haryana - 121001',
        city: 'Faridabad',
        state: 'Haryana',
        pincode: '121001',
        requiredBy: DateTime.now().add(const Duration(days: 5)),
        budgetMin: 75000,
        budgetMax: 85000,
        status: 'completed',
        bidCount: 12,
        createdAt: DateTime.now().subtract(const Duration(days: 10)),
        updatedAt: DateTime.now().subtract(const Duration(days: 5)),
      ),
      RequirementModel(
        id: 'req_4',
        builderId: 'builder_3',
        builderName: 'Modern Constructions',
        title: 'River Sand for Concrete Work',
        description: 'Fine grade river sand needed for concrete work. Should be clean and free from impurities.',
        materialType: 'sand',
        materialSubType: 'River Sand',
        quantity: 50,
        unit: 'cubic meters',
        deliveryLocation: 'Construction Site, Pune, Maharashtra - 411001',
        city: 'Pune',
        state: 'Maharashtra',
        pincode: '411001',
        requiredBy: DateTime.now().add(const Duration(days: 3)),
        budgetMin: 55000,
        budgetMax: 65000,
        status: 'active',
        bidCount: 3,
        createdAt: DateTime.now().subtract(const Duration(hours: 6)),
        updatedAt: DateTime.now().subtract(const Duration(hours: 6)),
      ),
    ];
  }

  // Sample bids
  static List<BidModel> getSampleBids() {
    return [
      BidModel(
        id: 'bid_1',
        requirementId: 'req_1',
        vendorId: 'vendor_1',
        vendorName: 'Rajesh Building Materials',
        vendorPhone: '+91 **********',
        bidAmount: 35000,
        pricePerUnit: 350,
        deliveryDays: 3,
        deliveryTerms: 'Free delivery within city limits',
        paymentTerms: '30% advance, 70% on delivery',
        description: 'ACC Cement Grade 53, 50kg bags. Free delivery within city limits.',
        status: 'active',
        validUntil: DateTime.now().add(const Duration(days: 2)),
        createdAt: DateTime.now().subtract(const Duration(hours: 4)),
        updatedAt: DateTime.now().subtract(const Duration(hours: 4)),
      ),
      BidModel(
        id: 'bid_2',
        requirementId: 'req_1',
        vendorId: 'vendor_2',
        vendorName: 'Sharma Construction Supplies',
        vendorPhone: '+91 **********',
        bidAmount: 33500,
        pricePerUnit: 335,
        deliveryDays: 2,
        deliveryTerms: 'Delivery within 48 hours',
        paymentTerms: '50% advance, 50% on delivery',
        description: 'UltraTech Cement Grade 53, best quality with warranty.',
        status: 'active',
        validUntil: DateTime.now().add(const Duration(days: 3)),
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
        updatedAt: DateTime.now().subtract(const Duration(hours: 2)),
      ),
    ];
  }

  // Sample products
  static List<ProductModel> getSampleProducts() {
    return [
      ProductModel(
        id: 'prod_1',
        vendorId: 'vendor_1',
        vendorName: 'Rajesh Building Materials',
        name: 'ACC Cement Grade 53',
        description: 'High quality cement suitable for all construction needs. OPC Grade 53 with superior strength.',
        category: 'cement',
        subCategory: 'OPC',
        price: 350.0,
        unit: 'bag',
        imageUrls: ['https://via.placeholder.com/300x200?text=ACC+Cement'],
        specifications: {
          'grade': '53',
          'type': 'OPC',
          'weight': '50kg',
          'brand': 'ACC'
        },
        isAvailable: true,
        stockQuantity: 500,
        minOrderQuantity: 10,
        deliveryInfo: 'Free delivery within 10km',
        tags: ['cement', 'construction', 'building'],
        rating: 4.5,
        reviewCount: 120,
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        updatedAt: DateTime.now(),
      ),
      ProductModel(
        id: 'prod_2',
        vendorId: 'vendor_2',
        vendorName: 'Sharma Steel Works',
        name: 'TMT Steel Bars 12mm',
        description: 'Fe500 grade TMT bars with superior strength and corrosion resistance.',
        category: 'steel',
        subCategory: 'TMT',
        price: 55.0,
        unit: 'kg',
        imageUrls: ['https://via.placeholder.com/300x200?text=TMT+Steel'],
        specifications: {
          'grade': 'Fe500',
          'diameter': '12mm',
          'length': '12m',
          'type': 'TMT'
        },
        isAvailable: true,
        stockQuantity: 1000,
        minOrderQuantity: 100,
        deliveryInfo: 'Delivery in 2-3 days',
        tags: ['steel', 'tmt', 'construction'],
        rating: 4.7,
        reviewCount: 85,
        createdAt: DateTime.now().subtract(const Duration(days: 20)),
        updatedAt: DateTime.now(),
      ),
      ProductModel(
        id: 'prod_3',
        vendorId: 'vendor_3',
        vendorName: 'Kumar Brick Works',
        name: 'Red Clay Bricks',
        description: 'High quality red clay bricks, perfect for construction. Class A quality.',
        category: 'bricks',
        subCategory: 'Clay',
        price: 8.5,
        unit: 'piece',
        imageUrls: ['https://via.placeholder.com/300x200?text=Red+Bricks'],
        specifications: {
          'type': 'Clay',
          'size': '230x110x75mm',
          'class': 'A',
          'color': 'Red'
        },
        isAvailable: true,
        stockQuantity: 5000,
        minOrderQuantity: 500,
        deliveryInfo: 'Bulk delivery available',
        tags: ['bricks', 'clay', 'construction'],
        rating: 4.3,
        reviewCount: 200,
        createdAt: DateTime.now().subtract(const Duration(days: 15)),
        updatedAt: DateTime.now(),
      ),
      ProductModel(
        id: 'prod_4',
        vendorId: 'vendor_4',
        vendorName: 'Asian Paints Store',
        name: 'Royale Emulsion Paint',
        description: 'Premium interior emulsion paint with excellent coverage and durability.',
        category: 'paints',
        subCategory: 'Interior',
        price: 1200.0,
        unit: 'litre',
        imageUrls: ['https://via.placeholder.com/300x200?text=Paint'],
        specifications: {
          'type': 'Emulsion',
          'finish': 'Matt',
          'coverage': '120 sq ft per litre',
          'brand': 'Asian Paints'
        },
        isAvailable: true,
        stockQuantity: 200,
        minOrderQuantity: 1,
        deliveryInfo: 'Same day delivery',
        tags: ['paint', 'interior', 'emulsion'],
        rating: 4.6,
        reviewCount: 150,
        createdAt: DateTime.now().subtract(const Duration(days: 10)),
        updatedAt: DateTime.now(),
      ),
      ProductModel(
        id: 'prod_5',
        vendorId: 'vendor_5',
        vendorName: 'River Sand Suppliers',
        name: 'Fine River Sand',
        description: 'Clean and fine river sand, perfect for concrete and plastering work.',
        category: 'sand',
        subCategory: 'River',
        price: 1500.0,
        unit: 'cubic meter',
        imageUrls: ['https://via.placeholder.com/300x200?text=River+Sand'],
        specifications: {
          'type': 'River',
          'grade': 'Fine',
          'moisture': 'Low',
          'silt': 'Minimal'
        },
        isAvailable: true,
        stockQuantity: 100,
        minOrderQuantity: 5,
        deliveryInfo: 'Truck delivery available',
        tags: ['sand', 'river', 'construction'],
        rating: 4.4,
        reviewCount: 75,
        createdAt: DateTime.now().subtract(const Duration(days: 5)),
        updatedAt: DateTime.now(),
      ),
    ];
  }

  // Sample services
  static List<ServiceModel> getSampleServices() {
    return [
      ServiceModel(
        id: 'service_1',
        name: 'Masonry Work',
        description: 'Expert masonry services including brickwork, stonework, and concrete work.',
        category: 'masonry',
        subcategories: ['Brickwork', 'Stonework', 'Concrete'],
        pricePerHour: 150.0,
        pricePerDay: 1200.0,
        unit: 'day',
        imageUrls: ['https://via.placeholder.com/300x200?text=Masonry'],
        providerId: 'mistri_1',
        providerName: 'Ramesh Kumar',
        providerPhone: '+91 **********',
        providerRating: 4.5,
        totalJobs: 150,
        yearsExperience: 8,
        skills: ['Brickwork', 'Concrete', 'Plastering', 'Foundation'],
        certifications: ['ITI Civil', 'Safety Training'],
        isAvailable: true,
        location: 'Sector 15, Gurgaon',
        city: 'Gurgaon',
        state: 'Haryana',
        radiusKm: 25.0,
        availability: {
          'monday': true,
          'tuesday': true,
          'wednesday': true,
          'thursday': true,
          'friday': true,
          'saturday': true,
          'sunday': false,
        },
        languages: ['Hindi', 'English'],
        createdAt: DateTime.now().subtract(const Duration(days: 60)),
        updatedAt: DateTime.now(),
        isVerified: true,
      ),
      ServiceModel(
        id: 'service_2',
        name: 'Plumbing Services',
        description: 'Complete plumbing solutions including installation, repair, and maintenance.',
        category: 'plumbing',
        subcategories: ['Installation', 'Repair', 'Maintenance'],
        pricePerHour: 200.0,
        pricePerDay: 1500.0,
        unit: 'hour',
        imageUrls: ['https://via.placeholder.com/300x200?text=Plumbing'],
        providerId: 'mistri_2',
        providerName: 'Suresh Sharma',
        providerPhone: '+91 **********',
        providerRating: 4.7,
        totalJobs: 200,
        yearsExperience: 12,
        skills: ['Pipe Installation', 'Leak Repair', 'Bathroom Fitting', 'Water Tank'],
        certifications: ['Plumbing License', 'Safety Certified'],
        isAvailable: true,
        location: 'DLF Phase 2, Gurgaon',
        city: 'Gurgaon',
        state: 'Haryana',
        radiusKm: 30.0,
        availability: {
          'monday': true,
          'tuesday': true,
          'wednesday': true,
          'thursday': true,
          'friday': true,
          'saturday': true,
          'sunday': true,
        },
        languages: ['Hindi', 'English', 'Punjabi'],
        createdAt: DateTime.now().subtract(const Duration(days: 45)),
        updatedAt: DateTime.now(),
        isVerified: true,
      ),
    ];
  }

  // Sample service bookings
  static List<ServiceBookingModel> getSampleServiceBookings(String userId) {
    return [
      ServiceBookingModel(
        id: 'booking_1',
        serviceId: 'service_1',
        serviceName: 'Masonry Work',
        providerId: 'mistri_1',
        providerName: 'Ramesh Kumar',
        providerPhone: '+91 **********',
        customerId: userId,
        customerName: 'John Doe',
        customerPhone: '+91 **********',
        bookingDate: DateTime.now().add(const Duration(days: 2)),
        timeSlot: '9:00 AM - 5:00 PM',
        numberOfDays: 3,
        totalAmount: 3600.0,
        workAddress: '123 Main Street, Sector 15',
        city: 'Gurgaon',
        state: 'Haryana',
        pincode: '122001',
        status: 'confirmed',
        paymentStatus: 'paid',
        notes: 'Need to complete boundary wall construction',
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        updatedAt: DateTime.now(),
      ),
    ];
  }

  // Get material categories
  static List<Map<String, dynamic>> getMaterialCategories() {
    return [
      {
        'id': 'cement',
        'name': 'Cement',
        'name_hi': 'सीमेंट',
        'icon': 'construction',
        'color': 0xFFFF9800,
      },
      {
        'id': 'steel',
        'name': 'Steel',
        'name_hi': 'स्टील',
        'icon': 'hardware',
        'color': 0xFF607D8B,
      },
      {
        'id': 'bricks',
        'name': 'Bricks',
        'name_hi': 'ईंट',
        'icon': 'view_module',
        'color': 0xFFD32F2F,
      },
      {
        'id': 'sand',
        'name': 'Sand',
        'name_hi': 'रेत',
        'icon': 'grain',
        'color': 0xFFFFC107,
      },
      {
        'id': 'aggregates',
        'name': 'Aggregates',
        'name_hi': 'एग्रीगेट',
        'icon': 'scatter_plot',
        'color': 0xFF795548,
      },
    ];
  }

  // Get sample vendors
  static List<VendorModel> getSampleVendors() {
    return [
      VendorModel(
        id: 'vendor_1',
        name: 'Rajesh Kumar',
        businessName: 'Kumar Construction Materials',
        profileImage: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
        specializations: ['Bricks', 'Cement', 'Sand'],
        location: LocationModel(
          latitude: 28.4595,
          longitude: 77.0266,
          address: 'Sector 15, Gurgaon, Haryana',
          city: 'Gurgaon',
          state: 'Haryana',
          pincode: '122001',
        ),
        phone: '+91 **********',
        email: '<EMAIL>',
        rating: 4.5,
        reviewCount: 127,
        isAvailable: true,
        isVerified: true,
        averagePrice: 450,
        averageDeliveryTime: 45,
        deliveryRadius: 15.0,
        minimumOrderValue: 500,
        freeDeliveryAvailable: true,
        freeDeliveryThreshold: 2000,
        createdAt: DateTime.now().subtract(const Duration(days: 365)),
        lastActive: DateTime.now().subtract(const Duration(hours: 2)),
      ),
      VendorModel(
        id: 'vendor_2',
        name: 'Priya Sharma',
        businessName: 'Sharma Steel & Cement',
        profileImage: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
        specializations: ['Steel', 'Cement', 'Tiles'],
        location: LocationModel(
          latitude: 28.4089,
          longitude: 77.0178,
          address: 'Cyber City, Gurgaon, Haryana',
          city: 'Gurgaon',
          state: 'Haryana',
          pincode: '122002',
        ),
        phone: '+91 **********',
        email: '<EMAIL>',
        rating: 4.8,
        reviewCount: 89,
        isAvailable: true,
        isVerified: true,
        averagePrice: 650,
        averageDeliveryTime: 30,
        deliveryRadius: 12.0,
        minimumOrderValue: 1000,
        freeDeliveryAvailable: true,
        freeDeliveryThreshold: 3000,
        createdAt: DateTime.now().subtract(const Duration(days: 200)),
        lastActive: DateTime.now().subtract(const Duration(minutes: 30)),
      ),
      VendorModel(
        id: 'vendor_3',
        name: 'Amit Singh',
        businessName: 'Singh Paints & Hardware',
        profileImage: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
        specializations: ['Paints', 'Hardware', 'Tools'],
        location: LocationModel(
          latitude: 28.4744,
          longitude: 77.0266,
          address: 'MG Road, Gurgaon, Haryana',
          city: 'Gurgaon',
          state: 'Haryana',
          pincode: '122001',
        ),
        phone: '+91 9876543212',
        email: '<EMAIL>',
        rating: 4.2,
        reviewCount: 156,
        isAvailable: true,
        isVerified: false,
        averagePrice: 320,
        averageDeliveryTime: 60,
        deliveryRadius: 8.0,
        minimumOrderValue: 300,
        freeDeliveryAvailable: false,
        createdAt: DateTime.now().subtract(const Duration(days: 150)),
        lastActive: DateTime.now().subtract(const Duration(hours: 1)),
      ),
      VendorModel(
        id: 'vendor_4',
        name: 'Sunita Devi',
        businessName: 'Devi Tiles & Marble',
        profileImage: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',
        specializations: ['Tiles', 'Marble', 'Granite'],
        location: LocationModel(
          latitude: 28.4200,
          longitude: 77.0400,
          address: 'DLF Phase 2, Gurgaon, Haryana',
          city: 'Gurgaon',
          state: 'Haryana',
          pincode: '122002',
        ),
        phone: '+91 9876543213',
        email: '<EMAIL>',
        rating: 4.6,
        reviewCount: 203,
        isAvailable: false,
        isVerified: true,
        averagePrice: 850,
        averageDeliveryTime: 90,
        deliveryRadius: 20.0,
        minimumOrderValue: 2000,
        freeDeliveryAvailable: true,
        freeDeliveryThreshold: 5000,
        createdAt: DateTime.now().subtract(const Duration(days: 300)),
        lastActive: DateTime.now().subtract(const Duration(hours: 8)),
      ),
      VendorModel(
        id: 'vendor_5',
        name: 'Mohammad Ali',
        businessName: 'Ali Sand & Aggregates',
        profileImage: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150',
        specializations: ['Sand', 'Gravel', 'Stone'],
        location: LocationModel(
          latitude: 28.4500,
          longitude: 77.0500,
          address: 'Sector 18, Gurgaon, Haryana',
          city: 'Gurgaon',
          state: 'Haryana',
          pincode: '122015',
        ),
        phone: '+91 9876543214',
        email: '<EMAIL>',
        rating: 4.3,
        reviewCount: 78,
        isAvailable: true,
        isVerified: true,
        averagePrice: 280,
        averageDeliveryTime: 120,
        deliveryRadius: 25.0,
        minimumOrderValue: 1500,
        freeDeliveryAvailable: true,
        freeDeliveryThreshold: 4000,
        createdAt: DateTime.now().subtract(const Duration(days: 180)),
        lastActive: DateTime.now().subtract(const Duration(minutes: 15)),
      ),
    ];
  }
}
